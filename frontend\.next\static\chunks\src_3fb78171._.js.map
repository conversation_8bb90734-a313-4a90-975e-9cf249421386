{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { UserRole } from '@/types/auth'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Loader2 } from 'lucide-react'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requiredRole?: UserRole\n  redirectTo?: string\n}\n\nexport function ProtectedRoute({\n  children,\n  requiredRole,\n  redirectTo = '/auth'\n}: ProtectedRouteProps) {\n  const { user, profile, loading, hasRole } = useAuth()\n  const router = useRouter()\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  useEffect(() => {\n    if (mounted && !loading) {\n      if (!user) {\n        router.push(redirectTo)\n        return\n      }\n\n      if (requiredRole && !hasRole(requiredRole)) {\n        router.push('/unauthorized')\n        return\n      }\n    }\n  }, [mounted, user, profile, loading, requiredRole, hasRole, router, redirectTo])\n\n  // Show loading until mounted and auth is resolved\n  if (!mounted || loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Card className=\"w-full max-w-md\">\n          <CardContent className=\"flex flex-col items-center justify-center py-8\">\n            <Loader2 className=\"h-8 w-8 animate-spin text-blue-600 mb-4\" />\n            <p className=\"text-gray-600 dark:text-gray-300\">جاري التحميل...</p>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null // Will redirect\n  }\n\n  if (requiredRole && !hasRole(requiredRole)) {\n    return null // Will redirect\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;;;AAPA;;;;;;AAeO,SAAS,eAAe,EAC7B,QAAQ,EACR,YAAY,EACZ,aAAa,OAAO,EACA;;IACpB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAClD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,WAAW;QACb;mCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,WAAW,CAAC,SAAS;gBACvB,IAAI,CAAC,MAAM;oBACT,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,IAAI,gBAAgB,CAAC,QAAQ,eAAe;oBAC1C,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF;QACF;mCAAG;QAAC;QAAS;QAAM;QAAS;QAAS;QAAc;QAAS;QAAQ;KAAW;IAE/E,kDAAkD;IAClD,IAAI,CAAC,WAAW,SAAS;QACvB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAK1D;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,KAAK,gBAAgB;;IAC9B;IAEA,IAAI,gBAAgB,CAAC,QAAQ,eAAe;QAC1C,OAAO,KAAK,gBAAgB;;IAC9B;IAEA,qBAAO;kBAAG;;AACZ;GAlDgB;;QAK8B,kIAAA,CAAA,UAAO;QACpC,qIAAA,CAAA,YAAS;;;KANV", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 768, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/admin/PageBuilder.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { PageProject, PageTemplate, AIGenerationRequest } from '@/types/page-builder'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { toast } from 'sonner'\nimport {\n  Wand2,\n  Layout,\n  Palette,\n  Eye,\n  Save,\n  Download,\n  Upload,\n  Plus,\n  Grid,\n  Smartphone,\n  Tablet,\n  Monitor,\n  Zap,\n  Sparkles,\n  RefreshCw,\n  GraduationCap,\n  Home,\n  ShoppingBag,\n  Info,\n  Phone,\n  Grid3X3,\n  FileText,\n  Link as LinkIcon,\n  ExternalLink,\n  User,\n  Settings\n} from 'lucide-react'\n\ninterface PageBuilderProps {\n  project?: PageProject\n  onSave?: (project: PageProject) => void\n  onPreview?: (project: PageProject) => void\n  onPublish?: (project: PageProject) => void\n}\n\n// دالة للحصول على مكون الأيقونة المناسب\nconst getIconComponent = (iconName: string) => {\n  const iconMap: { [key: string]: any } = {\n    'Home': Home,\n    'ShoppingBag': ShoppingBag,\n    'Info': Info,\n    'Phone': Phone,\n    'FileText': FileText,\n    'Link': LinkIcon,\n    'ExternalLink': ExternalLink,\n    'User': User,\n    'Settings': Settings,\n    'GraduationCap': GraduationCap,\n    'Grid3X3': Grid3X3\n  }\n\n  return iconMap[iconName] || Home\n}\n\nexport function PageBuilder({ project, onSave, onPreview, onPublish }: PageBuilderProps) {\n  const [currentProject, setCurrentProject] = useState<PageProject | null>(project || null)\n  const [templates, setTemplates] = useState<PageTemplate[]>([])\n  const [showAIDialog, setShowAIDialog] = useState(false)\n  const [showTemplateDialog, setShowTemplateDialog] = useState(false)\n  const [isGenerating, setIsGenerating] = useState(false)\n  const [previewDevice, setPreviewDevice] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')\n  const [aiPrompt, setAiPrompt] = useState('')\n  const [selectedTemplate, setSelectedTemplate] = useState<PageTemplate | null>(null)\n  const [includeMainHeader, setIncludeMainHeader] = useState(true)\n  const [mainMenuItems, setMainMenuItems] = useState<any[]>([])\n  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false)\n  const [selectedAIModel, setSelectedAIModel] = useState('')\n  const [pageCategory, setPageCategory] = useState('')\n  const [targetAudience, setTargetAudience] = useState('')\n  const [businessType, setBusinessType] = useState('')\n  const [selectedColors, setSelectedColors] = useState<string[]>(['#3B82F6', '#8B5CF6'])\n  const [includeImages, setIncludeImages] = useState(true)\n  const [includeText, setIncludeText] = useState(true)\n  const [pageStyle, setPageStyle] = useState('modern')\n  const [generationProgress, setGenerationProgress] = useState(0)\n  const [availableModels, setAvailableModels] = useState<any[]>([])\n  const [isLoadingModels, setIsLoadingModels] = useState(false)\n\n  // جلب القوالب\n  const fetchTemplates = async () => {\n    try {\n      const response = await fetch('/api/page-builder/templates')\n      const data = await response.json()\n\n      if (response.ok) {\n        setTemplates(data.templates)\n      } else {\n        toast.error(data.error || 'خطأ في جلب القوالب')\n      }\n    } catch (error) {\n      console.error('Error fetching templates:', error)\n      toast.error('خطأ في الاتصال بالخادم')\n    }\n  }\n\n  // جلب النماذج المتاحة\n  const fetchAvailableModels = async () => {\n    setIsLoadingModels(true)\n    try {\n      const response = await fetch('/api/ai-models')\n      const data = await response.json()\n      if (data.success) {\n        setAvailableModels(data.providers || [])\n        // تحديد النموذج الافتراضي\n        if (data.providers && data.providers.length > 0) {\n          const firstProvider = data.providers[0]\n          if (firstProvider.models && firstProvider.models.length > 0) {\n            setSelectedAIModel(`${firstProvider.id}-${firstProvider.models[0]}`)\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching AI models:', error)\n      toast.error('فشل في جلب نماذج الذكاء الاصطناعي')\n    } finally {\n      setIsLoadingModels(false)\n    }\n  }\n\n  // جلب عناصر القائمة الرئيسية\n  const fetchMainMenuItems = async () => {\n    try {\n      const response = await fetch('/api/menu-items')\n      const data = await response.json()\n\n      if (response.ok) {\n        setMainMenuItems(data.menuItems || [])\n      }\n    } catch (error) {\n      console.error('Error fetching menu items:', error)\n    }\n  }\n\n  useEffect(() => {\n    fetchTemplates()\n    fetchMainMenuItems()\n    fetchAvailableModels()\n  }, [])\n\n  // إنشاء مشروع جديد\n  const createNewProject = async (name: string, templateId?: string) => {\n    try {\n      const response = await fetch('/api/page-builder', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          name,\n          templateId,\n          generationMode: templateId ? 'template' : 'manual'\n        })\n      })\n      \n      const data = await response.json()\n      \n      if (response.ok) {\n        setCurrentProject(data.project)\n        toast.success(data.message)\n      } else {\n        toast.error(data.error)\n      }\n    } catch (error) {\n      console.error('Error creating project:', error)\n      toast.error('خطأ في إنشاء المشروع')\n    }\n  }\n\n  // توليد صفحة بالذكاء الاصطناعي\n  const generateWithAI = async () => {\n    if (!aiPrompt.trim()) {\n      toast.error('يرجى إدخال وصف للصفحة')\n      return\n    }\n\n    setIsGenerating(true)\n    setGenerationProgress(0)\n\n    // محاكاة تقدم التوليد\n    const progressInterval = setInterval(() => {\n      setGenerationProgress(prev => {\n        if (prev >= 90) {\n          clearInterval(progressInterval)\n          return 90\n        }\n        return prev + Math.random() * 15\n      })\n    }, 500)\n\n    try {\n      const generationRequest: AIGenerationRequest = {\n        prompt: aiPrompt,\n        language: 'ar',\n        category: pageCategory as any,\n        style: pageStyle,\n        colors: selectedColors,\n        includeImages: includeImages,\n        includeText: includeText,\n        pageType: pageCategory,\n        targetAudience: targetAudience,\n        businessType: businessType,\n        modelId: selectedAIModel,\n        includeMainHeader: includeMainHeader,\n        mainMenuItems: includeMainHeader ? mainMenuItems : []\n      }\n\n      const response = await fetch('/api/page-builder/generate', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(generationRequest)\n      })\n      \n      const data = await response.json()\n      \n      if (response.ok && data.success) {\n        // إنشاء مشروع جديد مع المكونات المولدة\n        const projectName = `مشروع مولد بالذكاء الاصطناعي - ${new Date().toLocaleDateString('ar-MA')}`\n        \n        const newProject: PageProject = {\n          id: Date.now().toString(),\n          name: projectName,\n          description: `مولد من: ${aiPrompt}`,\n          components: data.components || [],\n          generationMode: 'ai',\n          settings: {\n            title: projectName,\n            description: aiPrompt,\n            keywords: [],\n            language: 'ar',\n            direction: 'rtl'\n          },\n          isPublished: false,\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString(),\n          createdBy: 'admin-1',\n          version: 1\n        }\n\n        setCurrentProject(newProject)\n        setShowAIDialog(false)\n        setAiPrompt('')\n        \n        toast.success('تم توليد الصفحة بنجاح!')\n        \n        if (data.suggestions && data.suggestions.length > 0) {\n          toast.info(`اقتراحات للتحسين: ${data.suggestions[0]}`)\n        }\n      } else {\n        toast.error(data.error || 'فشل في توليد الصفحة')\n      }\n    } catch (error) {\n      console.error('Error generating page:', error)\n      toast.error('خطأ في توليد الصفحة')\n    } finally {\n      clearInterval(progressInterval)\n      setGenerationProgress(100)\n      setTimeout(() => {\n        setIsGenerating(false)\n        setGenerationProgress(0)\n      }, 1000)\n    }\n  }\n\n  // استخدام قالب\n  const useTemplate = async (template: PageTemplate) => {\n    try {\n      const projectName = `مشروع من قالب: ${template.nameAr}`\n      \n      const newProject: PageProject = {\n        id: Date.now().toString(),\n        name: projectName,\n        description: `مبني على قالب: ${template.nameAr}`,\n        components: template.components,\n        templateId: template.id,\n        generationMode: 'template',\n        settings: {\n          title: projectName,\n          description: template.description,\n          keywords: template.tags,\n          language: 'ar',\n          direction: 'rtl'\n        },\n        isPublished: false,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n        createdBy: 'admin-1',\n        version: 1\n      }\n\n      setCurrentProject(newProject)\n      setShowTemplateDialog(false)\n      \n      // تحديث إحصائيات استخدام القالب\n      await fetch('/api/page-builder/templates', {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          templateId: template.id,\n          action: 'increment_usage'\n        })\n      })\n      \n      toast.success('تم تطبيق القالب بنجاح!')\n    } catch (error) {\n      console.error('Error using template:', error)\n      toast.error('خطأ في تطبيق القالب')\n    }\n  }\n\n  // حفظ المشروع\n  const saveProject = async () => {\n    if (!currentProject) return\n\n    try {\n      await onSave?.(currentProject)\n      toast.success('تم حفظ المشروع بنجاح')\n    } catch (error) {\n      console.error('Error saving project:', error)\n      toast.error('خطأ في حفظ المشروع')\n    }\n  }\n\n  // معاينة المشروع\n  const previewProject = () => {\n    if (!currentProject) return\n    onPreview?.(currentProject)\n  }\n\n  // نشر المشروع\n  const publishProject = async () => {\n    if (!currentProject) return\n\n    try {\n      await onPublish?.(currentProject)\n      toast.success('تم نشر المشروع بنجاح')\n    } catch (error) {\n      console.error('Error publishing project:', error)\n      toast.error('خطأ في نشر المشروع')\n    }\n  }\n\n  const getDeviceIcon = (device: string) => {\n    switch (device) {\n      case 'desktop': return <Monitor className=\"h-4 w-4\" />\n      case 'tablet': return <Tablet className=\"h-4 w-4\" />\n      case 'mobile': return <Smartphone className=\"h-4 w-4\" />\n      default: return <Monitor className=\"h-4 w-4\" />\n    }\n  }\n\n  const getDeviceWidth = (device: string) => {\n    switch (device) {\n      case 'desktop': return '100%'\n      case 'tablet': return '768px'\n      case 'mobile': return '375px'\n      default: return '100%'\n    }\n  }\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      {/* شريط الأدوات */}\n      <div className=\"border-b bg-background p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-4\">\n            <h2 className=\"text-xl font-semibold arabic-text\">\n              {currentProject ? currentProject.name : 'بناء الصفحات الذكية'}\n            </h2>\n            {currentProject && (\n              <Badge variant=\"outline\">\n                {currentProject.generationMode === 'ai' ? '🤖 مولد بالذكاء الاصطناعي' :\n                 currentProject.generationMode === 'template' ? '📋 من قالب' : '✏️ يدوي'}\n              </Badge>\n            )}\n          </div>\n\n          <div className=\"flex items-center gap-2\">\n            {/* أزرار المعاينة */}\n            <div className=\"flex items-center border rounded-lg\">\n              {(['desktop', 'tablet', 'mobile'] as const).map((device) => (\n                <Button\n                  key={device}\n                  variant={previewDevice === device ? \"default\" : \"ghost\"}\n                  size=\"sm\"\n                  onClick={() => setPreviewDevice(device)}\n                  className=\"rounded-none first:rounded-l-lg last:rounded-r-lg\"\n                >\n                  {getDeviceIcon(device)}\n                </Button>\n              ))}\n            </div>\n\n            {currentProject && (\n              <>\n                <Button variant=\"outline\" onClick={previewProject}>\n                  <Eye className=\"h-4 w-4 mr-2\" />\n                  معاينة\n                </Button>\n                <Button variant=\"outline\" onClick={saveProject}>\n                  <Save className=\"h-4 w-4 mr-2\" />\n                  حفظ\n                </Button>\n                <Button onClick={publishProject}>\n                  <Upload className=\"h-4 w-4 mr-2\" />\n                  نشر\n                </Button>\n              </>\n            )}\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex-1 flex\">\n        {/* الشريط الجانبي */}\n        <div className=\"w-80 border-r bg-muted/30 p-4 space-y-4\">\n          <div className=\"space-y-2\">\n            <Button \n              onClick={() => setShowAIDialog(true)} \n              className=\"w-full justify-start\"\n              variant=\"outline\"\n            >\n              <Wand2 className=\"h-4 w-4 mr-2\" />\n              توليد بالذكاء الاصطناعي\n            </Button>\n            \n            <Button \n              onClick={() => setShowTemplateDialog(true)} \n              className=\"w-full justify-start\"\n              variant=\"outline\"\n            >\n              <Layout className=\"h-4 w-4 mr-2\" />\n              اختيار قالب\n            </Button>\n            \n            <Button \n              onClick={() => createNewProject('مشروع جديد')} \n              className=\"w-full justify-start\"\n              variant=\"outline\"\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              مشروع فارغ\n            </Button>\n          </div>\n\n          {currentProject && (\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"text-sm\">تفاصيل المشروع</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-2 text-sm\">\n                <div>\n                  <Label>الاسم:</Label>\n                  <p className=\"text-muted-foreground\">{currentProject.name}</p>\n                </div>\n                <div>\n                  <Label>المكونات:</Label>\n                  <p className=\"text-muted-foreground\">{currentProject.components.length}</p>\n                </div>\n                <div>\n                  <Label>آخر تحديث:</Label>\n                  <p className=\"text-muted-foreground\">\n                    {new Date(currentProject.updatedAt).toLocaleDateString('ar-MA')}\n                  </p>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </div>\n\n        {/* منطقة المعاينة */}\n        <div className=\"flex-1 p-4 bg-gray-50 dark:bg-gray-900\">\n          {currentProject ? (\n            <div className=\"h-full flex items-center justify-center\">\n              <div \n                className=\"bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden\"\n                style={{ \n                  width: getDeviceWidth(previewDevice),\n                  height: '80vh',\n                  maxWidth: '100%'\n                }}\n              >\n                <div className=\"h-full overflow-y-auto\">\n                  {currentProject.components.length > 0 ? (\n                    <div className=\"space-y-0\">\n                      {/* هيدر القائمة الرئيسية المحسن */}\n                      {includeMainHeader && (\n                        <div className=\"bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 shadow-lg p-4 sticky top-0 z-50\">\n                          <div className=\"max-w-7xl mx-auto flex items-center justify-between\">\n                            {/* الشعار والعنوان */}\n                            <div className=\"flex items-center gap-4\">\n                              <div className=\"flex items-center gap-3\">\n                                <div className=\"w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center\">\n                                  <GraduationCap className=\"h-6 w-6 text-white\" />\n                                </div>\n                                <div>\n                                  <div className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                                    منصة أزياء التخرج\n                                  </div>\n                                  <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                                    Graduation Toqs Platform\n                                  </div>\n                                </div>\n                              </div>\n                            </div>\n\n                            {/* القائمة الرئيسية */}\n                            <nav className=\"hidden lg:flex items-center gap-1\">\n                              {mainMenuItems.slice(0, 6).map((item, index) => {\n                                const IconComponent = getIconComponent(item.icon)\n                                return (\n                                  <a\n                                    key={index}\n                                    href=\"#\"\n                                    className=\"flex items-center gap-2 px-4 py-2.5 rounded-xl text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200\"\n                                  >\n                                    {IconComponent && <IconComponent className=\"h-4 w-4\" />}\n                                    <span>{item.title_ar || item.title}</span>\n                                  </a>\n                                )\n                              })}\n                            </nav>\n\n                            {/* أزرار التفاعل */}\n                            <div className=\"flex items-center gap-3\">\n                              <div className=\"hidden md:flex items-center gap-2\">\n                                <Button size=\"sm\" variant=\"ghost\" className=\"text-gray-600 dark:text-gray-400\">\n                                  تسجيل الدخول\n                                </Button>\n                                <Button size=\"sm\" className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white\">\n                                  إنشاء حساب\n                                </Button>\n                              </div>\n\n                              {/* قائمة الهاتف المحمول */}\n                              <Button size=\"sm\" variant=\"ghost\" className=\"lg:hidden\">\n                                <Grid3X3 className=\"h-4 w-4\" />\n                              </Button>\n                            </div>\n                          </div>\n                        </div>\n                      )}\n\n                      {/* مكونات الصفحة */}\n                      {currentProject.components.map((component, index) => (\n                        <div\n                          key={component.id}\n                          className=\"border-2 border-dashed border-transparent hover:border-blue-300 transition-colors group relative\"\n                          style={{\n                            height: component.size.height,\n                            backgroundColor: component.props.style?.backgroundColor || '#f9fafb',\n                            color: component.props.style?.color || '#111827',\n                            padding: component.props.style?.padding || '1rem',\n                            textAlign: component.props.style?.textAlign || 'right'\n                          }}\n                        >\n                          <div className=\"absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity\">\n                            <Badge variant=\"secondary\" className=\"text-xs\">\n                              {component.type}\n                            </Badge>\n                          </div>\n                          <div className=\"arabic-text\">\n                            {component.props.content || `مكون ${component.type}`}\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : (\n                    <div className=\"h-full flex items-center justify-center text-muted-foreground\">\n                      <div className=\"text-center\">\n                        <Layout className=\"h-12 w-12 mx-auto mb-4\" />\n                        <p>لا توجد مكونات بعد</p>\n                        <p className=\"text-sm\">ابدأ بإضافة مكونات للصفحة</p>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"h-full flex items-center justify-center\">\n              <div className=\"text-center\">\n                <Sparkles className=\"h-16 w-16 mx-auto mb-4 text-muted-foreground\" />\n                <h3 className=\"text-xl font-semibold mb-2\">مرحباً ببناء الصفحات الذكية</h3>\n                <p className=\"text-muted-foreground mb-6\">\n                  ابدأ بإنشاء صفحة جديدة باستخدام الذكاء الاصطناعي أو اختر قالباً جاهزاً\n                </p>\n                <div className=\"flex gap-2 justify-center\">\n                  <Button onClick={() => setShowAIDialog(true)}>\n                    <Wand2 className=\"h-4 w-4 mr-2\" />\n                    توليد بالذكاء الاصطناعي\n                  </Button>\n                  <Button variant=\"outline\" onClick={() => setShowTemplateDialog(true)}>\n                    <Layout className=\"h-4 w-4 mr-2\" />\n                    اختيار قالب\n                  </Button>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* حوار التوليد بالذكاء الاصطناعي */}\n      <Dialog open={showAIDialog} onOpenChange={setShowAIDialog}>\n        <DialogContent className=\"max-w-2xl\">\n          <DialogHeader>\n            <DialogTitle className=\"arabic-text flex items-center gap-2\">\n              <Wand2 className=\"h-5 w-5\" />\n              توليد صفحة بالذكاء الاصطناعي\n            </DialogTitle>\n            <DialogDescription>\n              صف الصفحة التي تريد إنشاءها وسيقوم الذكاء الاصطناعي بتوليدها لك\n            </DialogDescription>\n          </DialogHeader>\n\n          <div className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"aiPrompt\">وصف الصفحة *</Label>\n              <Textarea\n                id=\"aiPrompt\"\n                value={aiPrompt}\n                onChange={(e) => setAiPrompt(e.target.value)}\n                placeholder=\"مثال: أريد صفحة هبوط لشركة أزياء التخرج تتضمن قسم البطل وعرض المنتجات ونموذج اتصال...\"\n                rows={4}\n                className=\"arabic-text\"\n              />\n            </div>\n\n            {/* الخيارات المتقدمة */}\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <Label className=\"text-base font-medium\">الخيارات المتقدمة</Label>\n                <Button\n                  type=\"button\"\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}\n                >\n                  {showAdvancedOptions ? 'إخفاء' : 'إظهار'}\n                </Button>\n              </div>\n\n              {showAdvancedOptions && (\n                <div className=\"space-y-4 p-4 border rounded-lg bg-muted/30\">\n                  <Tabs defaultValue=\"basic\" className=\"w-full\">\n                    <TabsList className=\"grid w-full grid-cols-3\">\n                      <TabsTrigger value=\"basic\">الأساسيات</TabsTrigger>\n                      <TabsTrigger value=\"design\">التصميم</TabsTrigger>\n                      <TabsTrigger value=\"ai\">الذكاء الاصطناعي</TabsTrigger>\n                    </TabsList>\n\n                    <TabsContent value=\"basic\" className=\"space-y-4\">\n                      {/* فئة الصفحة */}\n                      <div className=\"space-y-2\">\n                        <Label>فئة الصفحة</Label>\n                        <Select value={pageCategory} onValueChange={setPageCategory}>\n                          <SelectTrigger>\n                            <SelectValue placeholder=\"اختر فئة الصفحة\" />\n                          </SelectTrigger>\n                          <SelectContent>\n                            <SelectItem value=\"landing\">صفحة هبوط</SelectItem>\n                            <SelectItem value=\"business\">صفحة أعمال</SelectItem>\n                            <SelectItem value=\"ecommerce\">متجر إلكتروني</SelectItem>\n                            <SelectItem value=\"portfolio\">معرض أعمال</SelectItem>\n                            <SelectItem value=\"blog\">مدونة</SelectItem>\n                            <SelectItem value=\"education\">تعليمية</SelectItem>\n                            <SelectItem value=\"fashion\">أزياء</SelectItem>\n                          </SelectContent>\n                        </Select>\n                      </div>\n\n                      {/* الجمهور المستهدف */}\n                      <div className=\"space-y-2\">\n                        <Label>الجمهور المستهدف</Label>\n                        <Input\n                          value={targetAudience}\n                          onChange={(e) => setTargetAudience(e.target.value)}\n                          placeholder=\"مثال: طلاب الجامعات، الخريجين الجدد\"\n                        />\n                      </div>\n\n                      {/* نوع العمل */}\n                      <div className=\"space-y-2\">\n                        <Label>نوع العمل</Label>\n                        <Input\n                          value={businessType}\n                          onChange={(e) => setBusinessType(e.target.value)}\n                          placeholder=\"مثال: تأجير أزياء التخرج، خدمات تعليمية\"\n                        />\n                      </div>\n\n                      {/* خيار تضمين الهيدر */}\n                      <div className=\"flex items-start space-x-3 space-x-reverse\">\n                        <input\n                          type=\"checkbox\"\n                          id=\"includeMainHeader\"\n                          checked={includeMainHeader}\n                          onChange={(e) => setIncludeMainHeader(e.target.checked)}\n                          className=\"rounded mt-1\"\n                        />\n                      <div className=\"flex-1\">\n                        <Label htmlFor=\"includeMainHeader\" className=\"text-sm font-medium\">\n                          تضمين هيدر القائمة الرئيسية المحسن\n                        </Label>\n                        <p className=\"text-xs text-muted-foreground mt-1\">\n                          إضافة هيدر احترافي مع شعار المنصة وعناصر التنقل الرئيسية\n                        </p>\n                      </div>\n                    </div>\n\n                    {/* معاينة الهيدر */}\n                    {includeMainHeader && (\n                      <div className=\"space-y-3\">\n                        <div className=\"text-xs text-muted-foreground bg-blue-50 dark:bg-blue-950/30 p-3 rounded border-r-4 border-blue-400\">\n                          <div className=\"flex items-center gap-2 mb-2\">\n                            <GraduationCap className=\"h-4 w-4 text-blue-600\" />\n                            <strong>معاينة الهيدر المضمن:</strong>\n                          </div>\n                          <ul className=\"space-y-1 text-xs\">\n                            <li>• شعار المنصة مع تصميم متدرج</li>\n                            <li>• عناصر التنقل مع أيقونات تفاعلية</li>\n                            <li>• أزرار تسجيل الدخول وإنشاء الحساب</li>\n                            <li>• دعم كامل للوضع الليلي والنهاري</li>\n                            <li>• تصميم متجاوب لجميع الأجهزة</li>\n                          </ul>\n                        </div>\n\n                        {/* عرض عناصر القائمة المتاحة */}\n                        {mainMenuItems.length > 0 && (\n                          <div className=\"bg-gray-50 dark:bg-gray-800/50 p-3 rounded\">\n                            <p className=\"text-xs font-medium mb-2\">عناصر القائمة المتاحة ({mainMenuItems.length}):</p>\n                            <div className=\"flex flex-wrap gap-1\">\n                              {mainMenuItems.slice(0, 6).map((item, index) => (\n                                <Badge key={index} variant=\"secondary\" className=\"text-xs\">\n                                  {item.title_ar || item.title}\n                                </Badge>\n                              ))}\n                              {mainMenuItems.length > 6 && (\n                                <Badge variant=\"outline\" className=\"text-xs\">\n                                  +{mainMenuItems.length - 6} المزيد\n                                </Badge>\n                              )}\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    )}\n                    </TabsContent>\n\n                    <TabsContent value=\"design\" className=\"space-y-4\">\n                      {/* نمط الصفحة */}\n                      <div className=\"space-y-2\">\n                        <Label>نمط التصميم</Label>\n                        <Select value={pageStyle} onValueChange={setPageStyle}>\n                          <SelectTrigger>\n                            <SelectValue placeholder=\"اختر نمط التصميم\" />\n                          </SelectTrigger>\n                          <SelectContent>\n                            <SelectItem value=\"modern\">عصري</SelectItem>\n                            <SelectItem value=\"classic\">كلاسيكي</SelectItem>\n                            <SelectItem value=\"minimal\">بسيط</SelectItem>\n                            <SelectItem value=\"elegant\">أنيق</SelectItem>\n                            <SelectItem value=\"creative\">إبداعي</SelectItem>\n                            <SelectItem value=\"professional\">احترافي</SelectItem>\n                          </SelectContent>\n                        </Select>\n                      </div>\n\n                      {/* الألوان */}\n                      <div className=\"space-y-2\">\n                        <Label>الألوان الأساسية</Label>\n                        <div className=\"grid grid-cols-4 gap-2\">\n                          {[\n                            '#3B82F6', '#8B5CF6', '#10B981', '#F59E0B',\n                            '#EF4444', '#6366F1', '#EC4899', '#14B8A6'\n                          ].map((color) => (\n                            <button\n                              key={color}\n                              type=\"button\"\n                              className={`w-12 h-12 rounded-lg border-2 transition-all ${\n                                selectedColors.includes(color)\n                                  ? 'border-gray-900 dark:border-white scale-110'\n                                  : 'border-gray-300 hover:scale-105'\n                              }`}\n                              style={{ backgroundColor: color }}\n                              onClick={() => {\n                                if (selectedColors.includes(color)) {\n                                  setSelectedColors(selectedColors.filter(c => c !== color))\n                                } else if (selectedColors.length < 3) {\n                                  setSelectedColors([...selectedColors, color])\n                                }\n                              }}\n                            />\n                          ))}\n                        </div>\n                        <p className=\"text-xs text-muted-foreground\">\n                          اختر حتى 3 ألوان ({selectedColors.length}/3)\n                        </p>\n                      </div>\n\n                      {/* خيارات المحتوى */}\n                      <div className=\"space-y-3\">\n                        <div className=\"flex items-center space-x-3 space-x-reverse\">\n                          <input\n                            type=\"checkbox\"\n                            id=\"includeImages\"\n                            checked={includeImages}\n                            onChange={(e) => setIncludeImages(e.target.checked)}\n                            className=\"rounded\"\n                          />\n                          <Label htmlFor=\"includeImages\">تضمين الصور</Label>\n                        </div>\n\n                        <div className=\"flex items-center space-x-3 space-x-reverse\">\n                          <input\n                            type=\"checkbox\"\n                            id=\"includeText\"\n                            checked={includeText}\n                            onChange={(e) => setIncludeText(e.target.checked)}\n                            className=\"rounded\"\n                          />\n                          <Label htmlFor=\"includeText\">تضمين النصوص</Label>\n                        </div>\n                      </div>\n                    </TabsContent>\n\n                    <TabsContent value=\"ai\" className=\"space-y-4\">\n                      {/* اختيار النموذج */}\n                      <div className=\"space-y-2\">\n                        <Label>نموذج الذكاء الاصطناعي</Label>\n                        <Select value={selectedAIModel} onValueChange={setSelectedAIModel} disabled={isLoadingModels}>\n                          <SelectTrigger>\n                            <SelectValue placeholder={isLoadingModels ? \"جاري التحميل...\" : \"اختر نموذج الذكاء الاصطناعي\"} />\n                          </SelectTrigger>\n                          <SelectContent>\n                            {availableModels.map((provider) => (\n                              provider.models?.map((model: string) => (\n                                <SelectItem key={`${provider.id}-${model}`} value={`${provider.id}-${model}`}>\n                                  {provider.providerName} - {model}\n                                </SelectItem>\n                              ))\n                            ))}\n                          </SelectContent>\n                        </Select>\n                        {isLoadingModels && (\n                          <p className=\"text-xs text-muted-foreground\">جاري تحميل النماذج المتاحة...</p>\n                        )}\n                      </div>\n\n                      {/* شريط التقدم */}\n                      {isGenerating && (\n                        <div className=\"space-y-2\">\n                          <div className=\"flex items-center justify-between\">\n                            <Label className=\"text-sm\">جاري التوليد...</Label>\n                            <span className=\"text-sm text-muted-foreground\">{Math.round(generationProgress)}%</span>\n                          </div>\n                          <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                            <div\n                              className=\"bg-blue-600 h-2 rounded-full transition-all duration-500\"\n                              style={{ width: `${generationProgress}%` }}\n                            />\n                          </div>\n                        </div>\n                      )}\n                    </TabsContent>\n                  </Tabs>\n                </div>\n              )}\n            </div>\n\n            <div className=\"bg-muted p-4 rounded-lg\">\n              <h4 className=\"font-medium mb-2\">نصائح للحصول على أفضل النتائج:</h4>\n              <ul className=\"text-sm text-muted-foreground space-y-1\">\n                <li>• كن محدداً في وصف نوع الصفحة (هبوط، منتج، شركة، إلخ)</li>\n                <li>• اذكر الأقسام المطلوبة (عن الشركة، المنتجات، الاتصال)</li>\n                <li>• حدد الألوان أو النمط المفضل إن أردت</li>\n                <li>• اذكر الجمهور المستهدف</li>\n                <li>• استخدم خيار \"تضمين الهيدر\" للحفاظ على تصميم الموقع الموحد</li>\n              </ul>\n            </div>\n          </div>\n\n          <DialogFooter>\n            <Button variant=\"outline\" onClick={() => setShowAIDialog(false)}>\n              إلغاء\n            </Button>\n            <Button\n              onClick={generateWithAI}\n              disabled={isGenerating || !aiPrompt.trim()}\n              className=\"relative overflow-hidden\"\n            >\n              {isGenerating ? (\n                <>\n                  <div className=\"flex items-center\">\n                    <Sparkles className=\"h-4 w-4 mr-2 animate-pulse\" />\n                    جاري التوليد... {Math.round(generationProgress)}%\n                  </div>\n                  <div\n                    className=\"absolute bottom-0 left-0 h-1 bg-white/30 transition-all duration-500\"\n                    style={{ width: `${generationProgress}%` }}\n                  />\n                </>\n              ) : (\n                <>\n                  <Zap className=\"h-4 w-4 mr-2\" />\n                  توليد صفحة احترافية\n                </>\n              )}\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* حوار اختيار القالب */}\n      <Dialog open={showTemplateDialog} onOpenChange={setShowTemplateDialog}>\n        <DialogContent className=\"max-w-4xl max-h-[80vh] overflow-y-auto\">\n          <DialogHeader>\n            <DialogTitle className=\"arabic-text\">اختيار قالب</DialogTitle>\n            <DialogDescription>\n              اختر قالباً جاهزاً لبدء مشروعك\n            </DialogDescription>\n          </DialogHeader>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {templates.map((template) => (\n              <Card \n                key={template.id} \n                className=\"cursor-pointer hover:shadow-lg transition-shadow\"\n                onClick={() => useTemplate(template)}\n              >\n                <div className=\"aspect-video bg-muted rounded-t-lg flex items-center justify-center\">\n                  <Layout className=\"h-8 w-8 text-muted-foreground\" />\n                </div>\n                <CardContent className=\"p-4\">\n                  <h3 className=\"font-semibold arabic-text\">{template.nameAr}</h3>\n                  <p className=\"text-sm text-muted-foreground mt-1\">\n                    {template.description}\n                  </p>\n                  <div className=\"flex items-center justify-between mt-3\">\n                    <Badge variant=\"outline\">{template.category}</Badge>\n                    <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n                      <span>{template.usageCount}</span>\n                      <span>استخدام</span>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n\n          {templates.length === 0 && (\n            <div className=\"text-center py-8\">\n              <Layout className=\"h-12 w-12 mx-auto mb-4 text-muted-foreground\" />\n              <p className=\"text-muted-foreground\">لا توجد قوالب متاحة حالياً</p>\n            </div>\n          )}\n\n          <DialogFooter>\n            <Button variant=\"outline\" onClick={() => setShowTemplateDialog(false)}>\n              إلغاء\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAQA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AA3BA;;;;;;;;;;;;;AA+DA,wCAAwC;AACxC,MAAM,mBAAmB,CAAC;IACxB,MAAM,UAAkC;QACtC,QAAQ,sMAAA,CAAA,OAAI;QACZ,eAAe,uNAAA,CAAA,cAAW;QAC1B,QAAQ,qMAAA,CAAA,OAAI;QACZ,SAAS,uMAAA,CAAA,QAAK;QACd,YAAY,iNAAA,CAAA,WAAQ;QACpB,QAAQ,qMAAA,CAAA,OAAQ;QAChB,gBAAgB,yNAAA,CAAA,eAAY;QAC5B,QAAQ,qMAAA,CAAA,OAAI;QACZ,YAAY,6MAAA,CAAA,WAAQ;QACpB,iBAAiB,2NAAA,CAAA,gBAAa;QAC9B,WAAW,+MAAA,CAAA,UAAO;IACpB;IAEA,OAAO,OAAO,CAAC,SAAS,IAAI,sMAAA,CAAA,OAAI;AAClC;AAEO,SAAS,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAoB;;;IACrF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,WAAW;IACpF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IACpF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAC9E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC5D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;QAAW;KAAU;IACrF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAChE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,cAAc;IACd,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,aAAa,KAAK,SAAS;YAC7B,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,sBAAsB;IACtB,MAAM,uBAAuB;QAC3B,mBAAmB;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,mBAAmB,KAAK,SAAS,IAAI,EAAE;gBACvC,0BAA0B;gBAC1B,IAAI,KAAK,SAAS,IAAI,KAAK,SAAS,CAAC,MAAM,GAAG,GAAG;oBAC/C,MAAM,gBAAgB,KAAK,SAAS,CAAC,EAAE;oBACvC,IAAI,cAAc,MAAM,IAAI,cAAc,MAAM,CAAC,MAAM,GAAG,GAAG;wBAC3D,mBAAmB,GAAG,cAAc,EAAE,CAAC,CAAC,EAAE,cAAc,MAAM,CAAC,EAAE,EAAE;oBACrE;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,6BAA6B;IAC7B,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,iBAAiB,KAAK,SAAS,IAAI,EAAE;YACvC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;YACA;YACA;QACF;gCAAG,EAAE;IAEL,mBAAmB;IACnB,MAAM,mBAAmB,OAAO,MAAc;QAC5C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA,gBAAgB,aAAa,aAAa;gBAC5C;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,kBAAkB,KAAK,OAAO;gBAC9B,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,KAAK,OAAO;YAC5B,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,KAAK;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,+BAA+B;IAC/B,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS,IAAI,IAAI;YACpB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,gBAAgB;QAChB,sBAAsB;QAEtB,sBAAsB;QACtB,MAAM,mBAAmB,YAAY;YACnC,sBAAsB,CAAA;gBACpB,IAAI,QAAQ,IAAI;oBACd,cAAc;oBACd,OAAO;gBACT;gBACA,OAAO,OAAO,KAAK,MAAM,KAAK;YAChC;QACF,GAAG;QAEH,IAAI;YACF,MAAM,oBAAyC;gBAC7C,QAAQ;gBACR,UAAU;gBACV,UAAU;gBACV,OAAO;gBACP,QAAQ;gBACR,eAAe;gBACf,aAAa;gBACb,UAAU;gBACV,gBAAgB;gBAChB,cAAc;gBACd,SAAS;gBACT,mBAAmB;gBACnB,eAAe,oBAAoB,gBAAgB,EAAE;YACvD;YAEA,MAAM,WAAW,MAAM,MAAM,8BAA8B;gBACzD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,IAAI,KAAK,OAAO,EAAE;gBAC/B,uCAAuC;gBACvC,MAAM,cAAc,CAAC,+BAA+B,EAAE,IAAI,OAAO,kBAAkB,CAAC,UAAU;gBAE9F,MAAM,aAA0B;oBAC9B,IAAI,KAAK,GAAG,GAAG,QAAQ;oBACvB,MAAM;oBACN,aAAa,CAAC,SAAS,EAAE,UAAU;oBACnC,YAAY,KAAK,UAAU,IAAI,EAAE;oBACjC,gBAAgB;oBAChB,UAAU;wBACR,OAAO;wBACP,aAAa;wBACb,UAAU,EAAE;wBACZ,UAAU;wBACV,WAAW;oBACb;oBACA,aAAa;oBACb,WAAW,IAAI,OAAO,WAAW;oBACjC,WAAW,IAAI,OAAO,WAAW;oBACjC,WAAW;oBACX,SAAS;gBACX;gBAEA,kBAAkB;gBAClB,gBAAgB;gBAChB,YAAY;gBAEZ,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,GAAG;oBACnD,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,kBAAkB,EAAE,KAAK,WAAW,CAAC,EAAE,EAAE;gBACvD;YACF,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;YACd,sBAAsB;YACtB,WAAW;gBACT,gBAAgB;gBAChB,sBAAsB;YACxB,GAAG;QACL;IACF;IAEA,eAAe;IACf,MAAM,cAAc,OAAO;QACzB,IAAI;YACF,MAAM,cAAc,CAAC,eAAe,EAAE,SAAS,MAAM,EAAE;YAEvD,MAAM,aAA0B;gBAC9B,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,MAAM;gBACN,aAAa,CAAC,eAAe,EAAE,SAAS,MAAM,EAAE;gBAChD,YAAY,SAAS,UAAU;gBAC/B,YAAY,SAAS,EAAE;gBACvB,gBAAgB;gBAChB,UAAU;oBACR,OAAO;oBACP,aAAa,SAAS,WAAW;oBACjC,UAAU,SAAS,IAAI;oBACvB,UAAU;oBACV,WAAW;gBACb;gBACA,aAAa;gBACb,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW;gBACX,SAAS;YACX;YAEA,kBAAkB;YAClB,sBAAsB;YAEtB,gCAAgC;YAChC,MAAM,MAAM,+BAA+B;gBACzC,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,YAAY,SAAS,EAAE;oBACvB,QAAQ;gBACV;YACF;YAEA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,cAAc;IACd,MAAM,cAAc;QAClB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,SAAS;YACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,iBAAiB;IACjB,MAAM,iBAAiB;QACrB,IAAI,CAAC,gBAAgB;QACrB,YAAY;IACd;IAEA,cAAc;IACd,MAAM,iBAAiB;QACrB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,YAAY;YAClB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,qBAAO,6LAAC,2MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC1C,KAAK;gBAAU,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YACxC,KAAK;gBAAU,qBAAO,6LAAC,iNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC5C;gBAAS,qBAAO,6LAAC,2MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;QACrC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,iBAAiB,eAAe,IAAI,GAAG;;;;;;gCAEzC,gCACC,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CACZ,eAAe,cAAc,KAAK,OAAO,8BACzC,eAAe,cAAc,KAAK,aAAa,eAAe;;;;;;;;;;;;sCAKrE,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACZ,AAAC;wCAAC;wCAAW;wCAAU;qCAAS,CAAW,GAAG,CAAC,CAAC,uBAC/C,6LAAC,qIAAA,CAAA,SAAM;4CAEL,SAAS,kBAAkB,SAAS,YAAY;4CAChD,MAAK;4CACL,SAAS,IAAM,iBAAiB;4CAChC,WAAU;sDAET,cAAc;2CANV;;;;;;;;;;gCAWV,gCACC;;sDACE,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS;;8DACjC,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGlC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS;;8DACjC,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS;;8DACf,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;wCACV,SAAQ;;0DAER,6LAAC,kNAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAIpC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,sBAAsB;wCACrC,WAAU;wCACV,SAAQ;;0DAER,6LAAC,wNAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAIrC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,iBAAiB;wCAChC,WAAU;wCACV,SAAQ;;0DAER,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;4BAKpC,gCACC,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAU;;;;;;;;;;;kDAEjC,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC;wDAAE,WAAU;kEAAyB,eAAe,IAAI;;;;;;;;;;;;0DAE3D,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC;wDAAE,WAAU;kEAAyB,eAAe,UAAU,CAAC,MAAM;;;;;;;;;;;;0DAExE,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC;wDAAE,WAAU;kEACV,IAAI,KAAK,eAAe,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASnE,6LAAC;wBAAI,WAAU;kCACZ,+BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,OAAO,eAAe;oCACtB,QAAQ;oCACR,UAAU;gCACZ;0CAEA,cAAA,6LAAC;oCAAI,WAAU;8CACZ,eAAe,UAAU,CAAC,MAAM,GAAG,kBAClC,6LAAC;wCAAI,WAAU;;4CAEZ,mCACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4EAAC,WAAU;;;;;;;;;;;kFAE3B,6LAAC;;0FACC,6LAAC;gFAAI,WAAU;0FAAkD;;;;;;0FAGjE,6LAAC;gFAAI,WAAU;0FAA2C;;;;;;;;;;;;;;;;;;;;;;;sEAQhE,6LAAC;4DAAI,WAAU;sEACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM;gEACpC,MAAM,gBAAgB,iBAAiB,KAAK,IAAI;gEAChD,qBACE,6LAAC;oEAEC,MAAK;oEACL,WAAU;;wEAET,+BAAiB,6LAAC;4EAAc,WAAU;;;;;;sFAC3C,6LAAC;sFAAM,KAAK,QAAQ,IAAI,KAAK,KAAK;;;;;;;mEAL7B;;;;;4DAQX;;;;;;sEAIF,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qIAAA,CAAA,SAAM;4EAAC,MAAK;4EAAK,SAAQ;4EAAQ,WAAU;sFAAmC;;;;;;sFAG/E,6LAAC,qIAAA,CAAA,SAAM;4EAAC,MAAK;4EAAK,WAAU;sFAAkG;;;;;;;;;;;;8EAMhI,6LAAC,qIAAA,CAAA,SAAM;oEAAC,MAAK;oEAAK,SAAQ;oEAAQ,WAAU;8EAC1C,cAAA,6LAAC,+MAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAQ5B,eAAe,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBACzC,6LAAC;oDAEC,WAAU;oDACV,OAAO;wDACL,QAAQ,UAAU,IAAI,CAAC,MAAM;wDAC7B,iBAAiB,UAAU,KAAK,CAAC,KAAK,EAAE,mBAAmB;wDAC3D,OAAO,UAAU,KAAK,CAAC,KAAK,EAAE,SAAS;wDACvC,SAAS,UAAU,KAAK,CAAC,KAAK,EAAE,WAAW;wDAC3C,WAAW,UAAU,KAAK,CAAC,KAAK,EAAE,aAAa;oDACjD;;sEAEA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAY,WAAU;0EAClC,UAAU,IAAI;;;;;;;;;;;sEAGnB,6LAAC;4DAAI,WAAU;sEACZ,UAAU,KAAK,CAAC,OAAO,IAAI,CAAC,KAAK,EAAE,UAAU,IAAI,EAAE;;;;;;;mDAhBjD,UAAU,EAAE;;;;;;;;;;6DAsBvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,wNAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAE;;;;;;8DACH,6LAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iDAQnC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAG1C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAS,IAAM,gBAAgB;;kEACrC,6LAAC,kNAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGpC,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,SAAS,IAAM,sBAAsB;;kEAC7D,6LAAC,wNAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWjD,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAc,cAAc;0BACxC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC,kNAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG/B,6LAAC,qIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAKrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,6LAAC,uIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,aAAY;4CACZ,MAAM;4CACN,WAAU;;;;;;;;;;;;8CAKd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAwB;;;;;;8DACzC,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,uBAAuB,CAAC;8DAEtC,sBAAsB,UAAU;;;;;;;;;;;;wCAIpC,qCACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gDAAC,cAAa;gDAAQ,WAAU;;kEACnC,6LAAC,mIAAA,CAAA,WAAQ;wDAAC,WAAU;;0EAClB,6LAAC,mIAAA,CAAA,cAAW;gEAAC,OAAM;0EAAQ;;;;;;0EAC3B,6LAAC,mIAAA,CAAA,cAAW;gEAAC,OAAM;0EAAS;;;;;;0EAC5B,6LAAC,mIAAA,CAAA,cAAW;gEAAC,OAAM;0EAAK;;;;;;;;;;;;kEAG1B,6LAAC,mIAAA,CAAA,cAAW;wDAAC,OAAM;wDAAQ,WAAU;;0EAEnC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,oIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,6LAAC,qIAAA,CAAA,SAAM;wEAAC,OAAO;wEAAc,eAAe;;0FAC1C,6LAAC,qIAAA,CAAA,gBAAa;0FACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;0FAE3B,6LAAC,qIAAA,CAAA,gBAAa;;kGACZ,6LAAC,qIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAU;;;;;;kGAC5B,6LAAC,qIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAW;;;;;;kGAC7B,6LAAC,qIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAY;;;;;;kGAC9B,6LAAC,qIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAY;;;;;;kGAC9B,6LAAC,qIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAO;;;;;;kGACzB,6LAAC,qIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAY;;;;;;kGAC9B,6LAAC,qIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAU;;;;;;;;;;;;;;;;;;;;;;;;0EAMlC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,oIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,6LAAC,oIAAA,CAAA,QAAK;wEACJ,OAAO;wEACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wEACjD,aAAY;;;;;;;;;;;;0EAKhB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,oIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,6LAAC,oIAAA,CAAA,QAAK;wEACJ,OAAO;wEACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wEAC/C,aAAY;;;;;;;;;;;;0EAKhB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,MAAK;wEACL,IAAG;wEACH,SAAS;wEACT,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,OAAO;wEACtD,WAAU;;;;;;kFAEd,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAoB,WAAU;0FAAsB;;;;;;0FAGnE,6LAAC;gFAAE,WAAU;0FAAqC;;;;;;;;;;;;;;;;;;4DAOrD,mCACC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;kGACb,6LAAC,2NAAA,CAAA,gBAAa;wFAAC,WAAU;;;;;;kGACzB,6LAAC;kGAAO;;;;;;;;;;;;0FAEV,6LAAC;gFAAG,WAAU;;kGACZ,6LAAC;kGAAG;;;;;;kGACJ,6LAAC;kGAAG;;;;;;kGACJ,6LAAC;kGAAG;;;;;;kGACJ,6LAAC;kGAAG;;;;;;kGACJ,6LAAC;kGAAG;;;;;;;;;;;;;;;;;;oEAKP,cAAc,MAAM,GAAG,mBACtB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAU;;oFAA2B;oFAAwB,cAAc,MAAM;oFAAC;;;;;;;0FACrF,6LAAC;gFAAI,WAAU;;oFACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACpC,6LAAC,oIAAA,CAAA,QAAK;4FAAa,SAAQ;4FAAY,WAAU;sGAC9C,KAAK,QAAQ,IAAI,KAAK,KAAK;2FADlB;;;;;oFAIb,cAAc,MAAM,GAAG,mBACtB,6LAAC,oIAAA,CAAA,QAAK;wFAAC,SAAQ;wFAAU,WAAU;;4FAAU;4FACzC,cAAc,MAAM,GAAG;4FAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAUzC,6LAAC,mIAAA,CAAA,cAAW;wDAAC,OAAM;wDAAS,WAAU;;0EAEpC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,oIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,6LAAC,qIAAA,CAAA,SAAM;wEAAC,OAAO;wEAAW,eAAe;;0FACvC,6LAAC,qIAAA,CAAA,gBAAa;0FACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;0FAE3B,6LAAC,qIAAA,CAAA,gBAAa;;kGACZ,6LAAC,qIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAS;;;;;;kGAC3B,6LAAC,qIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAU;;;;;;kGAC5B,6LAAC,qIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAU;;;;;;kGAC5B,6LAAC,qIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAU;;;;;;kGAC5B,6LAAC,qIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAW;;;;;;kGAC7B,6LAAC,qIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAe;;;;;;;;;;;;;;;;;;;;;;;;0EAMvC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,oIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,6LAAC;wEAAI,WAAU;kFACZ;4EACC;4EAAW;4EAAW;4EAAW;4EACjC;4EAAW;4EAAW;4EAAW;yEAClC,CAAC,GAAG,CAAC,CAAC,sBACL,6LAAC;gFAEC,MAAK;gFACL,WAAW,CAAC,6CAA6C,EACvD,eAAe,QAAQ,CAAC,SACpB,gDACA,mCACJ;gFACF,OAAO;oFAAE,iBAAiB;gFAAM;gFAChC,SAAS;oFACP,IAAI,eAAe,QAAQ,CAAC,QAAQ;wFAClC,kBAAkB,eAAe,MAAM,CAAC,CAAA,IAAK,MAAM;oFACrD,OAAO,IAAI,eAAe,MAAM,GAAG,GAAG;wFACpC,kBAAkB;+FAAI;4FAAgB;yFAAM;oFAC9C;gFACF;+EAdK;;;;;;;;;;kFAkBX,6LAAC;wEAAE,WAAU;;4EAAgC;4EACxB,eAAe,MAAM;4EAAC;;;;;;;;;;;;;0EAK7C,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFACC,MAAK;gFACL,IAAG;gFACH,SAAS;gFACT,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,OAAO;gFAClD,WAAU;;;;;;0FAEZ,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;0FAAgB;;;;;;;;;;;;kFAGjC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFACC,MAAK;gFACL,IAAG;gFACH,SAAS;gFACT,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,OAAO;gFAChD,WAAU;;;;;;0FAEZ,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;0FAAc;;;;;;;;;;;;;;;;;;;;;;;;kEAKnC,6LAAC,mIAAA,CAAA,cAAW;wDAAC,OAAM;wDAAK,WAAU;;0EAEhC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,oIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,6LAAC,qIAAA,CAAA,SAAM;wEAAC,OAAO;wEAAiB,eAAe;wEAAoB,UAAU;;0FAC3E,6LAAC,qIAAA,CAAA,gBAAa;0FACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;oFAAC,aAAa,kBAAkB,oBAAoB;;;;;;;;;;;0FAElE,6LAAC,qIAAA,CAAA,gBAAa;0FACX,gBAAgB,GAAG,CAAC,CAAC,WACpB,SAAS,MAAM,EAAE,IAAI,CAAC,sBACpB,6LAAC,qIAAA,CAAA,aAAU;4FAAiC,OAAO,GAAG,SAAS,EAAE,CAAC,CAAC,EAAE,OAAO;;gGACzE,SAAS,YAAY;gGAAC;gGAAI;;2FADZ,GAAG,SAAS,EAAE,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;;oEAOjD,iCACC,6LAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;4DAKhD,8BACC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,oIAAA,CAAA,QAAK;gFAAC,WAAU;0FAAU;;;;;;0FAC3B,6LAAC;gFAAK,WAAU;;oFAAiC,KAAK,KAAK,CAAC;oFAAoB;;;;;;;;;;;;;kFAElF,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EACC,WAAU;4EACV,OAAO;gFAAE,OAAO,GAAG,mBAAmB,CAAC,CAAC;4EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAW3D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAmB;;;;;;sDACjC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;sCAKV,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,gBAAgB;8CAAQ;;;;;;8CAGjE,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,gBAAgB,CAAC,SAAS,IAAI;oCACxC,WAAU;8CAET,6BACC;;0DACE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAA+B;oDAClC,KAAK,KAAK,CAAC;oDAAoB;;;;;;;0DAElD,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,GAAG,mBAAmB,CAAC,CAAC;gDAAC;;;;;;;qEAI7C;;0DACE,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0BAU5C,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAoB,cAAc;0BAC9C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,cAAW;oCAAC,WAAU;8CAAc;;;;;;8CACrC,6LAAC,qIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAKrB,6LAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC,mIAAA,CAAA,OAAI;oCAEH,WAAU;oCACV,OAAO,MAAE;;wCAAM,OAAA,YAAY;oCAAQ;;4CAApB;;;;sDAEf,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,wNAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAG,WAAU;8DAA6B,SAAS,MAAM;;;;;;8DAC1D,6LAAC;oDAAE,WAAU;8DACV,SAAS,WAAW;;;;;;8DAEvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAW,SAAS,QAAQ;;;;;;sEAC3C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAM,SAAS,UAAU;;;;;;8EAC1B,6LAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;mCAhBP,SAAS,EAAE;;;;;;;;;;wBAwBrB,UAAU,MAAM,KAAK,mBACpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,wNAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIzC,6LAAC,qIAAA,CAAA,eAAY;sCACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,sBAAsB;0CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnF;GA94BgB;KAAA", "debugId": null}}, {"offset": {"line": 3044, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/i18n.ts"], "sourcesContent": ["export type Locale = 'ar' | 'fr' | 'en';\n\nexport const locales: Locale[] = ['ar', 'fr', 'en'];\n\nexport const defaultLocale: Locale = 'ar';\n\nexport const localeNames = {\n  ar: 'العربية',\n  fr: 'Français', \n  en: 'English'\n};\n\nexport const localeFlags = {\n  ar: '🇲🇦',\n  fr: '🇫🇷',\n  en: '🇬🇧'\n};\n\nexport const rtlLocales: Locale[] = ['ar'];\n\nexport function isRtlLocale(locale: Locale): boolean {\n  return rtlLocales.includes(locale);\n}\n"], "names": [], "mappings": ";;;;;;;;AAEO,MAAM,UAAoB;IAAC;IAAM;IAAM;CAAK;AAE5C,MAAM,gBAAwB;AAE9B,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,aAAuB;IAAC;CAAK;AAEnC,SAAS,YAAY,MAAc;IACxC,OAAO,WAAW,QAAQ,CAAC;AAC7B", "debugId": null}}, {"offset": {"line": 3098, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/hooks/useTranslation.ts"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react';\nimport { Locale, defaultLocale } from '@/lib/i18n';\n\n// Import translation files\nimport arTranslations from '@/locales/ar.json';\nimport frTranslations from '@/locales/fr.json';\nimport enTranslations from '@/locales/en.json';\n\nconst translations = {\n  ar: arTranslations,\n  fr: frTranslations,\n  en: enTranslations,\n};\n\nexport function useTranslation() {\n  const [locale, setLocale] = useState<Locale>(defaultLocale);\n\n  useEffect(() => {\n    // Get locale from localStorage or use default\n    const savedLocale = localStorage.getItem('locale') as Locale;\n    if (savedLocale && ['ar', 'fr', 'en'].includes(savedLocale)) {\n      setLocale(savedLocale);\n    }\n  }, []);\n\n  const changeLocale = (newLocale: Locale) => {\n    setLocale(newLocale);\n    localStorage.setItem('locale', newLocale);\n    \n    // Update document direction and language\n    document.documentElement.lang = newLocale;\n    document.documentElement.dir = newLocale === 'ar' ? 'rtl' : 'ltr';\n  };\n\n  const t = (key: string): string => {\n    const keys = key.split('.');\n    let value: any = translations[locale];\n    \n    for (const k of keys) {\n      value = value?.[k];\n    }\n    \n    return value || key;\n  };\n\n  return {\n    locale,\n    changeLocale,\n    t,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAEA,2BAA2B;AAC3B;AACA;AACA;;AARA;;;;;;AAUA,MAAM,eAAe;IACnB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;AACpB;AAEO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,qHAAA,CAAA,gBAAa;IAE1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,8CAA8C;YAC9C,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,IAAI,eAAe;gBAAC;gBAAM;gBAAM;aAAK,CAAC,QAAQ,CAAC,cAAc;gBAC3D,UAAU;YACZ;QACF;mCAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,UAAU;QACV,aAAa,OAAO,CAAC,UAAU;QAE/B,yCAAyC;QACzC,SAAS,eAAe,CAAC,IAAI,GAAG;QAChC,SAAS,eAAe,CAAC,GAAG,GAAG,cAAc,OAAO,QAAQ;IAC9D;IAEA,MAAM,IAAI,CAAC;QACT,MAAM,OAAO,IAAI,KAAK,CAAC;QACvB,IAAI,QAAa,YAAY,CAAC,OAAO;QAErC,KAAK,MAAM,KAAK,KAAM;YACpB,QAAQ,OAAO,CAAC,EAAE;QACpB;QAEA,OAAO,SAAS;IAClB;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF;GApCgB", "debugId": null}}, {"offset": {"line": 3166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 3462, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/theme-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Moon, Sun } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\n\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\n\nexport function ThemeToggle() {\n  const { setTheme } = useTheme()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>\n          Light\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\n          Dark\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>\n          System\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AACA;;;AAPA;;;;;AAcO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAE5B,qBACE,6LAAC,+IAAA,CAAA,eAAY;;0BACX,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAU;;;;;;kCAGpD,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAS;;;;;;kCAGnD,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAW;;;;;;;;;;;;;;;;;;AAM7D;GAzBgB;;QACO,mJAAA,CAAA,WAAQ;;;KADf", "debugId": null}}, {"offset": {"line": 3579, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/language-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Languages, Check, Globe } from \"lucide-react\"\nimport { useTranslation } from \"@/hooks/useTranslation\"\nimport { Locale, localeNames, localeFlags } from \"@/lib/i18n\"\n\nimport { Button } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n} from \"@/components/ui/dropdown-menu\"\n\nexport function LanguageToggle() {\n  const { locale, changeLocale } = useTranslation()\n\n  const getCurrentLanguageInfo = () => {\n    return {\n      flag: localeFlags[locale],\n      name: localeNames[locale],\n      code: locale.toUpperCase()\n    }\n  }\n\n  const currentLang = getCurrentLanguageInfo()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"h-9 px-3 gap-2 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 group\"\n        >\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-lg group-hover:scale-110 transition-transform duration-300\">\n              {currentLang.flag}\n            </span>\n            <span className=\"hidden sm:inline-block text-sm font-medium\">\n              {currentLang.code}\n            </span>\n          </div>\n          <Globe className=\"h-4 w-4 opacity-60 group-hover:opacity-100 transition-opacity duration-300\" />\n          <span className=\"sr-only\">تغيير اللغة / Change language</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent\n        align=\"end\"\n        className=\"w-48 p-2\"\n        sideOffset={8}\n      >\n        <DropdownMenuLabel className=\"text-xs font-medium text-gray-500 dark:text-gray-400 px-2 py-1\">\n          {locale === 'ar' ? 'اختر اللغة' : locale === 'fr' ? 'Choisir la langue' : 'Choose Language'}\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        {Object.entries(localeNames).map(([code, name]) => (\n          <DropdownMenuItem\n            key={code}\n            onClick={() => changeLocale(code as Locale)}\n            className={`flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200 ${\n              locale === code\n                ? \"bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300\"\n                : \"hover:bg-gray-100 dark:hover:bg-gray-700\"\n            }`}\n          >\n            <span className=\"text-lg\">{localeFlags[code as Locale]}</span>\n            <div className=\"flex-1\">\n              <div className=\"font-medium text-sm\">{name}</div>\n              <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                {code === 'ar' ? 'العربية' : code === 'fr' ? 'Français' : 'English'}\n              </div>\n            </div>\n            {locale === code && (\n              <Check className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\n            )}\n          </DropdownMenuItem>\n        ))}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AAEA;AACA;;;AARA;;;;;;AAiBO,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAE9C,MAAM,yBAAyB;QAC7B,OAAO;YACL,MAAM,qHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,qHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,OAAO,WAAW;QAC1B;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,6LAAC,+IAAA,CAAA,eAAY;;0BACX,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;8CAEnB,6LAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;;;;;;;sCAGrB,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gBAClB,OAAM;gBACN,WAAU;gBACV,YAAY;;kCAEZ,6LAAC,+IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC1B,WAAW,OAAO,eAAe,WAAW,OAAO,sBAAsB;;;;;;kCAE5E,6LAAC,+IAAA,CAAA,wBAAqB;;;;;oBACrB,OAAO,OAAO,CAAC,qHAAA,CAAA,cAAW,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,iBAC5C,6LAAC,+IAAA,CAAA,mBAAgB;4BAEf,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,0FAA0F,EACpG,WAAW,OACP,qEACA,4CACJ;;8CAEF,6LAAC;oCAAK,WAAU;8CAAW,qHAAA,CAAA,cAAW,CAAC,KAAe;;;;;;8CACtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAuB;;;;;;sDACtC,6LAAC;4CAAI,WAAU;sDACZ,SAAS,OAAO,YAAY,SAAS,OAAO,aAAa;;;;;;;;;;;;gCAG7D,WAAW,sBACV,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;2BAhBd;;;;;;;;;;;;;;;;;AAuBjB;GAnEgB;;QACmB,iIAAA,CAAA,iBAAc;;;KADjC", "debugId": null}}, {"offset": {"line": 3766, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/auth/UserMenu.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { UserRole } from '@/types/auth'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { Button } from '@/components/ui/button'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar'\nimport { User, Settings, LogOut, Shield, School, Truck, GraduationCap } from 'lucide-react'\nimport Link from 'next/link'\n\nexport function UserMenu() {\n  const { user, profile, signOut } = useAuth()\n  const { t } = useTranslation()\n\n  if (!user || !profile) {\n    return (\n      <Button variant=\"outline\" asChild>\n        <a href=\"/auth\">{t('auth.login')}</a>\n      </Button>\n    )\n  }\n\n  const getRoleIcon = (role: UserRole) => {\n    switch (role) {\n      case UserRole.ADMIN:\n        return <Shield className=\"h-4 w-4\" />\n      case UserRole.SCHOOL:\n        return <School className=\"h-4 w-4\" />\n      case UserRole.DELIVERY:\n        return <Truck className=\"h-4 w-4\" />\n      case UserRole.STUDENT:\n        return <GraduationCap className=\"h-4 w-4\" />\n      default:\n        return <User className=\"h-4 w-4\" />\n    }\n  }\n\n  const getRoleLabel = (role: UserRole) => {\n    switch (role) {\n      case UserRole.ADMIN:\n        return 'مدير'\n      case UserRole.SCHOOL:\n        return 'مدرسة'\n      case UserRole.DELIVERY:\n        return 'شريك توصيل'\n      case UserRole.STUDENT:\n        return 'طالب'\n      default:\n        return 'مستخدم'\n    }\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  const handleSignOut = async () => {\n    await signOut()\n    // إعادة توجيه إلى الصفحة الرئيسية بعد تسجيل الخروج\n    window.location.href = '/'\n  }\n\n  const getDashboardUrl = () => {\n    if (!profile) return '/dashboard/student'\n\n    // توجيه كل دور إلى لوحة التحكم المخصصة له\n    switch (profile.role) {\n      case UserRole.ADMIN:\n        return '/dashboard/admin'\n      case UserRole.SCHOOL:\n        return '/dashboard/school'\n      case UserRole.DELIVERY:\n        return '/dashboard/delivery'\n      case UserRole.STUDENT:\n        return '/dashboard/student'\n      default:\n        return '/dashboard/student'\n    }\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <User className=\"h-[1.2rem] w-[1.2rem]\" />\n          <span className=\"sr-only\">User menu</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n        <DropdownMenuLabel className=\"font-normal\">\n          <div className=\"flex flex-col space-y-1\">\n            <p className=\"text-sm font-medium leading-none\">\n              {profile.full_name}\n            </p>\n            <p className=\"text-xs leading-none text-muted-foreground\">\n              {user.email}\n            </p>\n            <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n              {getRoleIcon(profile.role)}\n              <span>{getRoleLabel(profile.role)}</span>\n            </div>\n          </div>\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        \n        <DropdownMenuItem asChild>\n          <Link href=\"/profile\" className=\"cursor-pointer flex items-center\">\n            <User className=\"mr-2 h-4 w-4\" />\n            <span>{t('navigation.profile')}</span>\n          </Link>\n        </DropdownMenuItem>\n\n        <DropdownMenuItem asChild>\n          <Link href={getDashboardUrl()} className=\"cursor-pointer flex items-center\">\n            <Settings className=\"mr-2 h-4 w-4\" />\n            <span>{t('navigation.dashboard')}</span>\n          </Link>\n        </DropdownMenuItem>\n        \n        <DropdownMenuSeparator />\n        \n        <DropdownMenuItem \n          className=\"cursor-pointer text-red-600 focus:text-red-600\"\n          onClick={handleSignOut}\n        >\n          <LogOut className=\"mr-2 h-4 w-4\" />\n          <span>{t('auth.logout')}</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AASA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAhBA;;;;;;;;AAkBO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACzC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAE3B,IAAI,CAAC,QAAQ,CAAC,SAAS;QACrB,qBACE,6LAAC,qIAAA,CAAA,SAAM;YAAC,SAAQ;YAAU,OAAO;sBAC/B,cAAA,6LAAC;gBAAE,MAAK;0BAAS,EAAE;;;;;;;;;;;IAGzB;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK,uHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK,uHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK,uHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK,uHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK,uHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,gBAAgB;QACpB,MAAM;QACN,mDAAmD;QACnD,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,SAAS,OAAO;QAErB,0CAA0C;QAC1C,OAAQ,QAAQ,IAAI;YAClB,KAAK,uHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,+IAAA,CAAA,eAAY;;0BACX,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,WAAU;gBAAO,OAAM;gBAAM,UAAU;;kCAC1D,6LAAC,+IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC3B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CACV,QAAQ,SAAS;;;;;;8CAEpB,6LAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;8CAEb,6LAAC;oCAAI,WAAU;;wCACZ,YAAY,QAAQ,IAAI;sDACzB,6LAAC;sDAAM,aAAa,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;kCAItC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;kCAEtB,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAU;;8CAC9B,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;kCAIb,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM;4BAAmB,WAAU;;8CACvC,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;kCAIb,6LAAC,+IAAA,CAAA,wBAAqB;;;;;kCAEtB,6LAAC,+IAAA,CAAA,mBAAgB;wBACf,WAAU;wBACV,SAAS;;0CAET,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;0CAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAKnB;GA9HgB;;QACqB,kIAAA,CAAA,UAAO;QAC5B,iIAAA,CAAA,iBAAc;;;KAFd", "debugId": null}}, {"offset": {"line": 4112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 4148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/notifications/NotificationDropdown.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from 'react'\nimport { useNotifications, getNotificationIcon, getNotificationColor, formatNotificationTime } from '@/contexts/NotificationContext'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { ScrollArea } from '@/components/ui/scroll-area'\nimport { Separator } from '@/components/ui/separator'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { \n  Bell,\n  Check,\n  CheckCheck,\n  Trash2,\n  ExternalLink,\n  Settings,\n  X\n} from 'lucide-react'\n\nexport function NotificationDropdown() {\n  const { \n    notifications, \n    unreadCount, \n    markAsRead, \n    markAllAsRead, \n    removeNotification, \n    clearAll \n  } = useNotifications()\n  \n  const [isOpen, setIsOpen] = useState(false)\n\n  const handleNotificationClick = (notificationId: string, actionUrl?: string) => {\n    markAsRead(notificationId)\n    if (actionUrl) {\n      window.location.href = actionUrl\n    }\n  }\n\n  const recentNotifications = notifications.slice(0, 10)\n\n  return (\n    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n          <Bell className=\"h-5 w-5\" />\n          {unreadCount > 0 && (\n            <Badge \n              variant=\"destructive\" \n              className=\"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n            >\n              {unreadCount > 99 ? '99+' : unreadCount}\n            </Badge>\n          )}\n        </Button>\n      </DropdownMenuTrigger>\n      \n      <DropdownMenuContent \n        align=\"end\" \n        className=\"w-80 p-0\"\n        sideOffset={5}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-4 border-b\">\n          <h3 className=\"font-semibold arabic-text\">الإشعارات</h3>\n          <div className=\"flex items-center gap-2\">\n            {unreadCount > 0 && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={markAllAsRead}\n                className=\"h-8 px-2 text-xs arabic-text\"\n              >\n                <CheckCheck className=\"h-3 w-3 mr-1\" />\n                قراءة الكل\n              </Button>\n            )}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"h-8 w-8 p-0\"\n            >\n              <Settings className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n\n        {/* Notifications List */}\n        <ScrollArea className=\"h-96\">\n          {recentNotifications.length === 0 ? (\n            <div className=\"flex flex-col items-center justify-center py-8 text-center\">\n              <Bell className=\"h-12 w-12 text-gray-400 mb-3\" />\n              <p className=\"text-gray-500 arabic-text\">لا توجد إشعارات</p>\n              <p className=\"text-sm text-gray-400 arabic-text\">ستظهر إشعاراتك هنا</p>\n            </div>\n          ) : (\n            <div className=\"divide-y\">\n              {recentNotifications.map((notification) => (\n                <div\n                  key={notification.id}\n                  className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer ${\n                    !notification.isRead ? 'bg-blue-50/50 dark:bg-blue-900/10' : ''\n                  }`}\n                  onClick={() => handleNotificationClick(notification.id, notification.actionUrl)}\n                >\n                  <div className=\"flex items-start gap-3\">\n                    {/* Icon */}\n                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm ${\n                      getNotificationColor(notification.priority)\n                    }`}>\n                      {getNotificationIcon(notification.type)}\n                    </div>\n\n                    {/* Content */}\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-start justify-between\">\n                        <h4 className={`text-sm font-medium arabic-text ${\n                          !notification.isRead ? 'text-gray-900 dark:text-white' : 'text-gray-700 dark:text-gray-300'\n                        }`}>\n                          {notification.title}\n                        </h4>\n                        \n                        {/* Actions */}\n                        <div className=\"flex items-center gap-1 ml-2\">\n                          {!notification.isRead && (\n                            <div className=\"w-2 h-2 bg-blue-600 rounded-full\"></div>\n                          )}\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-red-100 hover:text-red-600\"\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              removeNotification(notification.id)\n                            }}\n                          >\n                            <X className=\"h-3 w-3\" />\n                          </Button>\n                        </div>\n                      </div>\n\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1 arabic-text line-clamp-2\">\n                        {notification.message}\n                      </p>\n\n                      <div className=\"flex items-center justify-between mt-2\">\n                        <span className=\"text-xs text-gray-500\">\n                          {formatNotificationTime(notification.createdAt)}\n                        </span>\n\n                        {notification.actionText && notification.actionUrl && (\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-6 px-2 text-xs text-blue-600 hover:text-blue-700 arabic-text\"\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              handleNotificationClick(notification.id, notification.actionUrl)\n                            }}\n                          >\n                            {notification.actionText}\n                            <ExternalLink className=\"h-3 w-3 mr-1\" />\n                          </Button>\n                        )}\n                      </div>\n\n                      {/* Expiry Warning */}\n                      {notification.expiresAt && (\n                        <div className=\"mt-2\">\n                          <Badge variant=\"outline\" className=\"text-xs arabic-text\">\n                            ينتهي في {formatNotificationTime(notification.expiresAt)}\n                          </Badge>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </ScrollArea>\n\n        {/* Footer */}\n        {notifications.length > 0 && (\n          <>\n            <Separator />\n            <div className=\"p-3 flex items-center justify-between\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"text-xs arabic-text\"\n                asChild\n              >\n                <a href=\"/notifications\">عرض جميع الإشعارات</a>\n              </Button>\n              \n              {notifications.length > 0 && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={clearAll}\n                  className=\"text-xs text-red-600 hover:text-red-700 arabic-text\"\n                >\n                  <Trash2 className=\"h-3 w-3 mr-1\" />\n                  حذف الكل\n                </Button>\n              )}\n            </div>\n          </>\n        )}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n\n// مكون إشعار منبثق للإشعارات الهامة\ninterface ToastNotificationProps {\n  notification: {\n    id: string\n    title: string\n    message: string\n    type: string\n    priority: string\n  }\n  onClose: () => void\n  onAction?: () => void\n}\n\nexport function ToastNotification({ notification, onClose, onAction }: ToastNotificationProps) {\n  return (\n    <div className={`fixed top-4 right-4 z-50 w-80 p-4 rounded-lg shadow-lg border ${\n      getNotificationColor(notification.priority as any)\n    } animate-in slide-in-from-right duration-300`}>\n      <div className=\"flex items-start gap-3\">\n        <div className=\"flex-shrink-0 text-lg\">\n          {getNotificationIcon(notification.type as any)}\n        </div>\n        \n        <div className=\"flex-1\">\n          <h4 className=\"font-medium arabic-text\">{notification.title}</h4>\n          <p className=\"text-sm mt-1 arabic-text\">{notification.message}</p>\n          \n          <div className=\"flex items-center gap-2 mt-3\">\n            {onAction && (\n              <Button size=\"sm\" onClick={onAction} className=\"arabic-text\">\n                عرض\n              </Button>\n            )}\n            <Button variant=\"ghost\" size=\"sm\" onClick={onClose}>\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// مكون عداد الإشعارات البسيط\nexport function NotificationBadge() {\n  const { unreadCount } = useNotifications()\n\n  if (unreadCount === 0) return null\n\n  return (\n    <Badge \n      variant=\"destructive\" \n      className=\"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n    >\n      {unreadCount > 99 ? '99+' : unreadCount}\n    </Badge>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAbA;;;;;;;;;AAuBO,SAAS;;IACd,MAAM,EACJ,aAAa,EACb,WAAW,EACX,UAAU,EACV,aAAa,EACb,kBAAkB,EAClB,QAAQ,EACT,GAAG,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,0BAA0B,CAAC,gBAAwB;QACvD,WAAW;QACX,IAAI,WAAW;YACb,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,MAAM,sBAAsB,cAAc,KAAK,CAAC,GAAG;IAEnD,qBACE,6LAAC,+IAAA,CAAA,eAAY;QAAC,MAAM;QAAQ,cAAc;;0BACxC,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAK,WAAU;;sCAC1C,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACf,cAAc,mBACb,6LAAC,oIAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,WAAU;sCAET,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAMpC,6LAAC,+IAAA,CAAA,sBAAmB;gBAClB,OAAM;gBACN,WAAU;gBACV,YAAY;;kCAGZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4B;;;;;;0CAC1C,6LAAC;gCAAI,WAAU;;oCACZ,cAAc,mBACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAI3C,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM1B,6LAAC,6IAAA,CAAA,aAAU;wBAAC,WAAU;kCACnB,oBAAoB,MAAM,KAAK,kBAC9B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAE,WAAU;8CAA4B;;;;;;8CACzC,6LAAC;oCAAE,WAAU;8CAAoC;;;;;;;;;;;iDAGnD,6LAAC;4BAAI,WAAU;sCACZ,oBAAoB,GAAG,CAAC,CAAC,6BACxB,6LAAC;oCAEC,WAAW,CAAC,6EAA6E,EACvF,CAAC,aAAa,MAAM,GAAG,sCAAsC,IAC7D;oCACF,SAAS,IAAM,wBAAwB,aAAa,EAAE,EAAE,aAAa,SAAS;8CAE9E,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAW,CAAC,4EAA4E,EAC3F,CAAA,GAAA,0IAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,QAAQ,GAC1C;0DACC,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,IAAI;;;;;;0DAIxC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAW,CAAC,gCAAgC,EAC9C,CAAC,aAAa,MAAM,GAAG,kCAAkC,oCACzD;0EACC,aAAa,KAAK;;;;;;0EAIrB,6LAAC;gEAAI,WAAU;;oEACZ,CAAC,aAAa,MAAM,kBACnB,6LAAC;wEAAI,WAAU;;;;;;kFAEjB,6LAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,mBAAmB,aAAa,EAAE;wEACpC;kFAEA,cAAA,6LAAC,+LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAKnB,6LAAC;wDAAE,WAAU;kEACV,aAAa,OAAO;;;;;;kEAGvB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,0IAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS;;;;;;4DAG/C,aAAa,UAAU,IAAI,aAAa,SAAS,kBAChD,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,wBAAwB,aAAa,EAAE,EAAE,aAAa,SAAS;gEACjE;;oEAEC,aAAa,UAAU;kFACxB,6LAAC,yNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;;;;;;;;;;;;;oDAM7B,aAAa,SAAS,kBACrB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;gEAAsB;gEAC7C,CAAA,GAAA,0IAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS;;;;;;;;;;;;;;;;;;;;;;;;mCAvE5D,aAAa,EAAE;;;;;;;;;;;;;;;oBAoF7B,cAAc,MAAM,GAAG,mBACtB;;0CACE,6LAAC,wIAAA,CAAA,YAAS;;;;;0CACV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,OAAO;kDAEP,cAAA,6LAAC;4CAAE,MAAK;sDAAiB;;;;;;;;;;;oCAG1B,cAAc,MAAM,GAAG,mBACtB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;GAjMgB;;QAQV,0IAAA,CAAA,mBAAgB;;;KARN;AAgNT,SAAS,kBAAkB,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAA0B;IAC3F,qBACE,6LAAC;QAAI,WAAW,CAAC,8DAA8D,EAC7E,CAAA,GAAA,0IAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,QAAQ,EAC3C,4CAA4C,CAAC;kBAC5C,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACZ,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,IAAI;;;;;;8BAGxC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2B,aAAa,KAAK;;;;;;sCAC3D,6LAAC;4BAAE,WAAU;sCAA4B,aAAa,OAAO;;;;;;sCAE7D,6LAAC;4BAAI,WAAU;;gCACZ,0BACC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAS;oCAAU,WAAU;8CAAc;;;;;;8CAI/D,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,SAAS;8CACzC,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B;MA5BgB;AA+BT,SAAS;;IACd,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD;IAEvC,IAAI,gBAAgB,GAAG,OAAO;IAE9B,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,SAAQ;QACR,WAAU;kBAET,cAAc,KAAK,QAAQ;;;;;;AAGlC;IAbgB;;QACU,0IAAA,CAAA,mBAAgB;;;MAD1B", "debugId": null}}, {"offset": {"line": 4688, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/Navigation.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Button } from '@/components/ui/button'\nimport { ThemeToggle } from '@/components/theme-toggle'\nimport { LanguageToggle } from '@/components/language-toggle'\nimport { UserMenu } from '@/components/auth/UserMenu'\nimport { NotificationDropdown } from '@/components/notifications/NotificationDropdown'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  GraduationCap,\n  Home,\n  ShoppingBag,\n  Palette,\n  Info,\n  Phone,\n  Search,\n  Heart,\n  ExternalLink,\n  FileText,\n  Link as LinkIcon,\n  ChevronDown,\n  Grid3X3\n} from 'lucide-react'\n\n// أنواع البيانات لعناصر القائمة\ninterface MenuItem {\n  id: string\n  title_ar: string\n  title_en?: string\n  title_fr?: string\n  slug: string\n  icon?: string\n  parent_id?: string\n  order_index: number\n  is_active: boolean\n  target_type: 'internal' | 'external' | 'page'\n  target_value: string\n}\n\n// نوع عنصر القائمة المعروض\ninterface NavItem {\n  href: string\n  label: string\n  icon?: React.ReactElement\n  target_type: 'internal' | 'external' | 'page'\n  subItems?: {\n    href: string\n    label: string\n    target_type: 'internal' | 'external' | 'page'\n  }[]\n}\n\nexport function Navigation() {\n  const { t, locale } = useTranslation()\n  const pathname = usePathname()\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n  const [cartItemsCount, setCartItemsCount] = useState(0)\n  const [wishlistCount, setWishlistCount] = useState(0)\n  const [menuItems, setMenuItems] = useState<MenuItem[]>([])\n  const [loading, setLoading] = useState(true)\n\n  // Close mobile menu when route changes\n  useEffect(() => {\n    setIsMobileMenuOpen(false)\n  }, [pathname])\n\n  // Mock cart and wishlist counts - replace with actual data\n  useEffect(() => {\n    // Simulate getting cart items from localStorage or API\n    const cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]')\n    setCartItemsCount(cartItems.length)\n\n    const wishlistItems = JSON.parse(localStorage.getItem('wishlistItems') || '[]')\n    setWishlistCount(wishlistItems.length)\n  }, [])\n\n  // جلب عناصر القائمة من قاعدة البيانات\n  useEffect(() => {\n    const fetchMenuItems = async () => {\n      try {\n        setLoading(true)\n        // جلب جميع عناصر القائمة (الرئيسية والفرعية)\n        const response = await fetch('/api/menu-items')\n\n        if (response.ok) {\n          const data = await response.json()\n          setMenuItems(data.menuItems || [])\n        } else {\n          // في حالة فشل API، استخدم القائمة الافتراضية\n          console.warn('Failed to fetch menu items from API, using default menu')\n          setMenuItems([])\n        }\n      } catch (error) {\n        // في حالة خطأ في الشبكة، استخدم القائمة الافتراضية\n        console.warn('Error fetching menu items, using default menu:', error)\n        setMenuItems([])\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    fetchMenuItems()\n  }, [])\n\n  // تحويل عناصر القائمة من قاعدة البيانات إلى تنسيق المكون\n  const getNavItemsFromDB = () => {\n    // فلترة العناصر الرئيسية فقط (التي ليس لها parent_id) والمفعلة\n    const mainItems = menuItems.filter(item => !item.parent_id && item.is_active)\n\n    return mainItems.map(item => {\n      // اختيار العنوان حسب اللغة الحالية\n      let label = item.title_ar // افتراضي\n      if (locale === 'en' && item.title_en) {\n        label = item.title_en\n      } else if (locale === 'fr' && item.title_fr) {\n        label = item.title_fr\n      }\n\n      // تحديد الرابط حسب نوع الهدف\n      let href = item.target_value\n      if (item.target_type === 'page') {\n        href = `/pages/${item.target_value}`\n      }\n\n      // تحديد الأيقونة\n      let icon = <LinkIcon className=\"h-4 w-4\" />\n      if (item.icon) {\n        // يمكن إضافة منطق لتحويل اسم الأيقونة إلى مكون\n        switch (item.icon) {\n          case 'Home':\n            icon = <Home className=\"h-4 w-4\" />\n            break\n          case 'ShoppingBag':\n            icon = <ShoppingBag className=\"h-4 w-4\" />\n            break\n          case 'Palette':\n            icon = <Palette className=\"h-4 w-4\" />\n            break\n          case 'Search':\n            icon = <Search className=\"h-4 w-4\" />\n            break\n          case 'Info':\n            icon = <Info className=\"h-4 w-4\" />\n            break\n          case 'Phone':\n            icon = <Phone className=\"h-4 w-4\" />\n            break\n          case 'Grid3X3':\n            icon = <Grid3X3 className=\"h-4 w-4\" />\n            break\n          case 'ExternalLink':\n            icon = <ExternalLink className=\"h-4 w-4\" />\n            break\n          case 'FileText':\n            icon = <FileText className=\"h-4 w-4\" />\n            break\n          default:\n            icon = <LinkIcon className=\"h-4 w-4\" />\n        }\n      }\n\n      // البحث عن القوائم الفرعية لهذا العنصر\n      const subItems = menuItems\n        .filter(subItem => subItem.parent_id === item.id && subItem.is_active)\n        .map(subItem => {\n          let subLabel = subItem.title_ar\n          if (locale === 'en' && subItem.title_en) {\n            subLabel = subItem.title_en\n          } else if (locale === 'fr' && subItem.title_fr) {\n            subLabel = subItem.title_fr\n          }\n\n          let subHref = subItem.target_value\n          if (subItem.target_type === 'page') {\n            subHref = `/pages/${subItem.target_value}`\n          }\n\n          return {\n            href: subHref,\n            label: subLabel,\n            target_type: subItem.target_type\n          }\n        })\n\n      return {\n        href,\n        label,\n        icon,\n        target_type: item.target_type,\n        subItems: subItems.length > 0 ? subItems : undefined\n      }\n    })\n  }\n\n  // القائمة الافتراضية (للاستخدام في حالة عدم وجود عناصر من قاعدة البيانات)\n  const defaultNavItems: NavItem[] = [\n    {\n      href: '/',\n      label: t('navigation.home'),\n      icon: <Home className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/catalog',\n      label: t('navigation.catalog'),\n      icon: <ShoppingBag className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/about',\n      label: t('navigation.about') || 'من نحن',\n      icon: <Info className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/contact',\n      label: t('navigation.contact') || 'تواصل معنا',\n      icon: <Phone className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    }\n  ]\n\n  // استخدام عناصر القائمة من قاعدة البيانات أو الافتراضية\n  const navItems = loading ? defaultNavItems : (menuItems.length > 0 ? getNavItemsFromDB() : defaultNavItems)\n\n  // استخدام عناصر القائمة فقط (بدون لوحة التحكم في القائمة الرئيسية)\n  const { user, profile } = useAuth()\n  const allNavItems = navItems\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/'\n    }\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <header className=\"bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50 transition-all duration-300\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center py-3\">\n          {/* Logo */}\n          <Link\n            href=\"/\"\n            className=\"flex items-center gap-3 hover:opacity-80 transition-all duration-300 group\"\n          >\n            <div className=\"relative\">\n              <GraduationCap className=\"h-9 w-9 text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300\" />\n              <div className=\"absolute -inset-1 bg-blue-600/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n            </div>\n            <div className=\"flex flex-col\">\n              <span className=\"text-xl font-bold text-gray-900 dark:text-white leading-tight\">\n                Graduation Toqs\n              </span>\n              <span className=\"text-xs text-blue-600 dark:text-blue-400 font-medium\">\n                {locale === 'ar' ? 'منصة أزياء التخرج' : locale === 'fr' ? 'Plateforme de Remise des Diplômes' : 'Graduation Platform'}\n              </span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center gap-1\">\n            {allNavItems.map((item) => {\n              // تحديد ما إذا كان الرابط خارجي\n              const isExternal = item.target_type === 'external'\n              const hasSubItems = item.subItems && item.subItems.length > 0\n\n              // إذا كان العنصر له قوائم فرعية\n              if (hasSubItems) {\n                return (\n                  <div key={item.href} className=\"relative group\">\n                    <button\n                      className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 ${\n                        isActive(item.href)\n                          ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                          : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                      }`}\n                    >\n                      <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>\n                        {item.icon}\n                      </span>\n                      <span className=\"text-sm font-medium\">\n                        {item.label}\n                      </span>\n                      <ChevronDown className=\"h-3 w-3 transition-transform group-hover:rotate-180\" />\n                    </button>\n\n                    {/* القائمة الفرعية */}\n                    <div className=\"absolute top-full left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                      <div className=\"py-2\">\n                        {item.subItems?.map((subItem) => {\n                          const subIsExternal = subItem.target_type === 'external'\n                          const subLinkProps = subIsExternal\n                            ? { href: subItem.href, target: '_blank', rel: 'noopener noreferrer' }\n                            : { href: subItem.href }\n\n                          return (\n                            <Link\n                              key={subItem.href}\n                              {...subLinkProps}\n                              className=\"flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                            >\n                              {subItem.label}\n                              {subIsExternal && (\n                                <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                              )}\n                            </Link>\n                          )\n                        })}\n                      </div>\n                    </div>\n                  </div>\n                )\n              }\n\n              // العناصر العادية بدون قوائم فرعية\n              const linkProps = isExternal\n                ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }\n                : { href: item.href }\n\n              return (\n                <Link\n                  key={item.href}\n                  {...linkProps}\n                  className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 ${\n                    isActive(item.href)\n                      ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                      : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                  }`}\n                >\n                  <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>\n                    {item.icon}\n                  </span>\n                  <span className=\"text-sm font-medium\">\n                    {item.label}\n                  </span>\n                  {isExternal && (\n                    <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                  )}\n                  {isActive(item.href) && (\n                    <div className=\"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-white rounded-full\"></div>\n                  )}\n                </Link>\n              )\n            })}\n          </nav>\n\n          {/* Desktop Actions */}\n          <div className=\"hidden lg:flex items-center gap-2\">\n            {/* Wishlist */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative group\" asChild>\n              <Link href=\"/wishlist\">\n                <Heart className=\"h-5 w-5 transition-colors group-hover:text-red-500\" />\n                {wishlistCount > 0 && (\n                  <Badge\n                    variant=\"destructive\"\n                    className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs animate-pulse\"\n                  >\n                    {wishlistCount > 99 ? '99+' : wishlistCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Cart Icon */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative group\" asChild>\n              <Link href=\"/cart\">\n                <ShoppingBag className=\"h-5 w-5 transition-colors group-hover:text-blue-600\" />\n                {cartItemsCount > 0 && (\n                  <Badge\n                    className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-blue-600 hover:bg-blue-700 animate-pulse\"\n                  >\n                    {cartItemsCount > 99 ? '99+' : cartItemsCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Notifications */}\n            <NotificationDropdown />\n\n            {/* Divider */}\n            <div className=\"h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1\"></div>\n\n            {/* Language & Theme Controls */}\n            <div className=\"flex items-center gap-1\">\n              <LanguageToggle />\n              <ThemeToggle />\n            </div>\n\n            {/* User Menu */}\n            <UserMenu />\n          </div>\n\n          {/* Mobile Actions */}\n          <div className=\"flex lg:hidden items-center gap-2\">\n            {/* Mobile Cart */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative\" asChild>\n              <Link href=\"/cart\">\n                <ShoppingBag className=\"h-5 w-5\" />\n                {cartItemsCount > 0 && (\n                  <Badge\n                    className=\"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs bg-blue-600\"\n                  >\n                    {cartItemsCount > 9 ? '9+' : cartItemsCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Mobile Menu Button */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"relative\"\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            >\n              <div className=\"relative w-6 h-6 flex items-center justify-center\">\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${\n                    isMobileMenuOpen ? 'rotate-45' : '-translate-y-1.5'\n                  }`}\n                />\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transition-all duration-300 ${\n                    isMobileMenuOpen ? 'opacity-0' : 'opacity-100'\n                  }`}\n                />\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${\n                    isMobileMenuOpen ? '-rotate-45' : 'translate-y-1.5'\n                  }`}\n                />\n              </div>\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <div\n          className={`lg:hidden overflow-hidden transition-all duration-300 ease-in-out ${\n            isMobileMenuOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'\n          }`}\n        >\n          <div className=\"border-t border-gray-200 dark:border-gray-700 py-4\">\n            <nav className=\"flex flex-col gap-1 mb-6\">\n              {allNavItems.map((item, index) => {\n                // تحديد ما إذا كان الرابط خارجي\n                const isExternal = item.target_type === 'external'\n                const hasSubItems = item.subItems && item.subItems.length > 0\n\n                // إذا كان العنصر له قوائم فرعية\n                if (hasSubItems) {\n                  return (\n                    <div key={item.href} className=\"mx-2\">\n                      <div\n                        className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 font-medium ${\n                          isActive(item.href)\n                            ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                            : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                        }`}\n                        style={{\n                          animationName: isMobileMenuOpen ? 'slideInFromRight' : 'none',\n                          animationDuration: '0.3s',\n                          animationTimingFunction: 'ease-out',\n                          animationFillMode: 'forwards',\n                          animationDelay: `${index * 50}ms`\n                        }}\n                      >\n                        <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>\n                          {item.icon}\n                        </span>\n                        <span className=\"text-sm flex-1\">\n                          {item.label}\n                        </span>\n                        <ChevronDown className=\"h-4 w-4\" />\n                      </div>\n\n                      {/* القوائم الفرعية للموبايل */}\n                      <div className=\"ml-6 mt-2 space-y-1\">\n                        {item.subItems?.map((subItem) => {\n                          const subIsExternal = subItem.target_type === 'external'\n                          const subLinkProps = subIsExternal\n                            ? { href: subItem.href, target: '_blank', rel: 'noopener noreferrer' }\n                            : { href: subItem.href }\n\n                          return (\n                            <Link\n                              key={subItem.href}\n                              {...subLinkProps}\n                              onClick={() => setIsMobileMenuOpen(false)}\n                              className=\"flex items-center gap-2 px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors\"\n                            >\n                              {subItem.label}\n                              {subIsExternal && (\n                                <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                              )}\n                            </Link>\n                          )\n                        })}\n                      </div>\n                    </div>\n                  )\n                }\n\n                // العناصر العادية بدون قوائم فرعية\n                const linkProps = isExternal\n                  ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }\n                  : { href: item.href }\n\n                return (\n                  <Link\n                    key={item.href}\n                    {...linkProps}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                    className={`flex items-center gap-3 px-4 py-3 mx-2 rounded-xl transition-all duration-300 font-medium ${\n                      isActive(item.href)\n                        ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                        : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                    }`}\n                    style={{\n                      animationName: isMobileMenuOpen ? 'slideInFromRight' : 'none',\n                      animationDuration: '0.3s',\n                      animationTimingFunction: 'ease-out',\n                      animationFillMode: 'forwards',\n                      animationDelay: `${index * 50}ms`\n                    }}\n                  >\n                    <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>\n                      {item.icon}\n                    </span>\n                    <span className=\"text-sm\">\n                      {item.label}\n                    </span>\n                    {isExternal && (\n                      <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                    )}\n                  </Link>\n                )\n              })}\n            </nav>\n\n            {/* Mobile Actions */}\n            <div className=\"flex items-center justify-between px-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n              <div className=\"flex items-center gap-2\">\n                <LanguageToggle />\n                <ThemeToggle />\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"relative\" asChild>\n                  <Link href=\"/wishlist\">\n                    <Heart className=\"h-5 w-5\" />\n                    {wishlistCount > 0 && (\n                      <Badge\n                        variant=\"destructive\"\n                        className=\"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs\"\n                      >\n                        {wishlistCount > 9 ? '9+' : wishlistCount}\n                      </Badge>\n                    )}\n                  </Link>\n                </Button>\n                <UserMenu />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n\nexport default Navigation\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAbA;;;;;;;;;;;;;AAyDO,SAAS;;IACd,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,oBAAoB;QACtB;+BAAG;QAAC;KAAS;IAEb,2DAA2D;IAC3D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,uDAAuD;YACvD,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;YAClE,kBAAkB,UAAU,MAAM;YAElC,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;YAC1E,iBAAiB,cAAc,MAAM;QACvC;+BAAG,EAAE;IAEL,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;uDAAiB;oBACrB,IAAI;wBACF,WAAW;wBACX,6CAA6C;wBAC7C,MAAM,WAAW,MAAM,MAAM;wBAE7B,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,aAAa,KAAK,SAAS,IAAI,EAAE;wBACnC,OAAO;4BACL,6CAA6C;4BAC7C,QAAQ,IAAI,CAAC;4BACb,aAAa,EAAE;wBACjB;oBACF,EAAE,OAAO,OAAO;wBACd,mDAAmD;wBACnD,QAAQ,IAAI,CAAC,kDAAkD;wBAC/D,aAAa,EAAE;oBACjB,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;+BAAG,EAAE;IAEL,yDAAyD;IACzD,MAAM,oBAAoB;QACxB,+DAA+D;QAC/D,MAAM,YAAY,UAAU,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,SAAS,IAAI,KAAK,SAAS;QAE5E,OAAO,UAAU,GAAG,CAAC,CAAA;YACnB,mCAAmC;YACnC,IAAI,QAAQ,KAAK,QAAQ,CAAC,UAAU;;YACpC,IAAI,WAAW,QAAQ,KAAK,QAAQ,EAAE;gBACpC,QAAQ,KAAK,QAAQ;YACvB,OAAO,IAAI,WAAW,QAAQ,KAAK,QAAQ,EAAE;gBAC3C,QAAQ,KAAK,QAAQ;YACvB;YAEA,6BAA6B;YAC7B,IAAI,OAAO,KAAK,YAAY;YAC5B,IAAI,KAAK,WAAW,KAAK,QAAQ;gBAC/B,OAAO,CAAC,OAAO,EAAE,KAAK,YAAY,EAAE;YACtC;YAEA,iBAAiB;YACjB,IAAI,qBAAO,6LAAC,qMAAA,CAAA,OAAQ;gBAAC,WAAU;;;;;;YAC/B,IAAI,KAAK,IAAI,EAAE;gBACb,+CAA+C;gBAC/C,OAAQ,KAAK,IAAI;oBACf,KAAK;wBACH,qBAAO,6LAAC,sMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACvB;oBACF,KAAK;wBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAC9B;oBACF,KAAK;wBACH,qBAAO,6LAAC,2MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAC1B;oBACF,KAAK;wBACH,qBAAO,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBACzB;oBACF,KAAK;wBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACvB;oBACF,KAAK;wBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACxB;oBACF,KAAK;wBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAC1B;oBACF,KAAK;wBACH,qBAAO,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAC/B;oBACF,KAAK;wBACH,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC3B;oBACF;wBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAQ;4BAAC,WAAU;;;;;;gBAC/B;YACF;YAEA,uCAAuC;YACvC,MAAM,WAAW,UACd,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,KAAK,KAAK,EAAE,IAAI,QAAQ,SAAS,EACpE,GAAG,CAAC,CAAA;gBACH,IAAI,WAAW,QAAQ,QAAQ;gBAC/B,IAAI,WAAW,QAAQ,QAAQ,QAAQ,EAAE;oBACvC,WAAW,QAAQ,QAAQ;gBAC7B,OAAO,IAAI,WAAW,QAAQ,QAAQ,QAAQ,EAAE;oBAC9C,WAAW,QAAQ,QAAQ;gBAC7B;gBAEA,IAAI,UAAU,QAAQ,YAAY;gBAClC,IAAI,QAAQ,WAAW,KAAK,QAAQ;oBAClC,UAAU,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;gBAC5C;gBAEA,OAAO;oBACL,MAAM;oBACN,OAAO;oBACP,aAAa,QAAQ,WAAW;gBAClC;YACF;YAEF,OAAO;gBACL;gBACA;gBACA;gBACA,aAAa,KAAK,WAAW;gBAC7B,UAAU,SAAS,MAAM,GAAG,IAAI,WAAW;YAC7C;QACF;IACF;IAEA,0EAA0E;IAC1E,MAAM,kBAA6B;QACjC;YACE,MAAM;YACN,OAAO,EAAE;YACT,oBAAM,6LAAC,sMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE;YACT,oBAAM,6LAAC,uNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE,uBAAuB;YAChC,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE,yBAAyB;YAClC,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,aAAa;QACf;KACD;IAED,wDAAwD;IACxD,MAAM,WAAW,UAAU,kBAAmB,UAAU,MAAM,GAAG,IAAI,sBAAsB;IAE3F,mEAAmE;IACnE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,cAAc;IAEpB,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAgE;;;;;;sDAGhF,6LAAC;4CAAK,WAAU;sDACb,WAAW,OAAO,sBAAsB,WAAW,OAAO,sCAAsC;;;;;;;;;;;;;;;;;;sCAMvG,6LAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC;gCAChB,gCAAgC;gCAChC,MAAM,aAAa,KAAK,WAAW,KAAK;gCACxC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;gCAE5D,gCAAgC;gCAChC,IAAI,aAAa;oCACf,qBACE,6LAAC;wCAAoB,WAAU;;0DAC7B,6LAAC;gDACC,WAAW,CAAC,sGAAsG,EAChH,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;;kEAEF,6LAAC;wDAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,yBAAyB;kEAChH,KAAK,IAAI;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEACb,KAAK,KAAK;;;;;;kEAEb,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;0DAIzB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,EAAE,IAAI,CAAC;wDACnB,MAAM,gBAAgB,QAAQ,WAAW,KAAK;wDAC9C,MAAM,eAAe,gBACjB;4DAAE,MAAM,QAAQ,IAAI;4DAAE,QAAQ;4DAAU,KAAK;wDAAsB,IACnE;4DAAE,MAAM,QAAQ,IAAI;wDAAC;wDAEzB,qBACE,6LAAC,+JAAA,CAAA,UAAI;4DAEF,GAAG,YAAY;4DAChB,WAAU;;gEAET,QAAQ,KAAK;gEACb,+BACC,6LAAC,yNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;2DANrB,QAAQ,IAAI;;;;;oDAUvB;;;;;;;;;;;;uCAtCI,KAAK,IAAI;;;;;gCA2CvB;gCAEA,mCAAmC;gCACnC,MAAM,YAAY,aACd;oCAAE,MAAM,KAAK,IAAI;oCAAE,QAAQ;oCAAU,KAAK;gCAAsB,IAChE;oCAAE,MAAM,KAAK,IAAI;gCAAC;gCAEtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEF,GAAG,SAAS;oCACb,WAAW,CAAC,sGAAsG,EAChH,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;;sDAEF,6LAAC;4CAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,yBAAyB;sDAChH,KAAK,IAAI;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;wCAEZ,4BACC,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAEzB,SAAS,KAAK,IAAI,mBACjB,6LAAC;4CAAI,WAAU;;;;;;;mCAlBZ,KAAK,IAAI;;;;;4BAsBpB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAiB,OAAO;8CAClE,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAChB,gBAAgB,mBACf,6LAAC,oIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAU;0DAET,gBAAgB,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAOtC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAiB,OAAO;8CAClE,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,iBAAiB,mBAChB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,WAAU;0DAET,iBAAiB,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAOvC,6LAAC,8JAAA,CAAA,uBAAoB;;;;;8CAGrB,6LAAC;oCAAI,WAAU;;;;;;8CAGf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2IAAA,CAAA,iBAAc;;;;;sDACf,6LAAC,wIAAA,CAAA,cAAW;;;;;;;;;;;8CAId,6LAAC,yIAAA,CAAA,WAAQ;;;;;;;;;;;sCAIX,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAW,OAAO;8CAC5D,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,iBAAiB,mBAChB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,WAAU;0DAET,iBAAiB,IAAI,OAAO;;;;;;;;;;;;;;;;;8CAOrC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB,CAAC;8CAEpC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,mBAAmB,cAAc,oBACjC;;;;;;0DAEJ,6LAAC;gDACC,WAAW,CAAC,0DAA0D,EACpE,mBAAmB,cAAc,eACjC;;;;;;0DAEJ,6LAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,mBAAmB,eAAe,mBAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQZ,6LAAC;oBACC,WAAW,CAAC,kEAAkE,EAC5E,mBAAmB,6BAA6B,qBAChD;8BAEF,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,MAAM;oCACtB,gCAAgC;oCAChC,MAAM,aAAa,KAAK,WAAW,KAAK;oCACxC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;oCAE5D,gCAAgC;oCAChC,IAAI,aAAa;wCACf,qBACE,6LAAC;4CAAoB,WAAU;;8DAC7B,6LAAC;oDACC,WAAW,CAAC,qFAAqF,EAC/F,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;oDACF,OAAO;wDACL,eAAe,mBAAmB,qBAAqB;wDACvD,mBAAmB;wDACnB,yBAAyB;wDACzB,mBAAmB;wDACnB,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;oDACnC;;sEAEA,6LAAC;4DAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,IAAI;sEAC3F,KAAK,IAAI;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEACb,KAAK,KAAK;;;;;;sEAEb,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;8DAIzB,6LAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,EAAE,IAAI,CAAC;wDACnB,MAAM,gBAAgB,QAAQ,WAAW,KAAK;wDAC9C,MAAM,eAAe,gBACjB;4DAAE,MAAM,QAAQ,IAAI;4DAAE,QAAQ;4DAAU,KAAK;wDAAsB,IACnE;4DAAE,MAAM,QAAQ,IAAI;wDAAC;wDAEzB,qBACE,6LAAC,+JAAA,CAAA,UAAI;4DAEF,GAAG,YAAY;4DAChB,SAAS,IAAM,oBAAoB;4DACnC,WAAU;;gEAET,QAAQ,KAAK;gEACb,+BACC,6LAAC,yNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;2DAPrB,QAAQ,IAAI;;;;;oDAWvB;;;;;;;2CA7CM,KAAK,IAAI;;;;;oCAiDvB;oCAEA,mCAAmC;oCACnC,MAAM,YAAY,aACd;wCAAE,MAAM,KAAK,IAAI;wCAAE,QAAQ;wCAAU,KAAK;oCAAsB,IAChE;wCAAE,MAAM,KAAK,IAAI;oCAAC;oCAEtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;wCAEF,GAAG,SAAS;wCACb,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC,0FAA0F,EACpG,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;wCACF,OAAO;4CACL,eAAe,mBAAmB,qBAAqB;4CACvD,mBAAmB;4CACnB,yBAAyB;4CACzB,mBAAmB;4CACnB,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;wCACnC;;0DAEA,6LAAC;gDAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,IAAI;0DAC3F,KAAK,IAAI;;;;;;0DAEZ,6LAAC;gDAAK,WAAU;0DACb,KAAK,KAAK;;;;;;4CAEZ,4BACC,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;uCAvBrB,KAAK,IAAI;;;;;gCA2BpB;;;;;;0CAIF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2IAAA,CAAA,iBAAc;;;;;0DACf,6LAAC,wIAAA,CAAA,cAAW;;;;;;;;;;;kDAEd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,WAAU;gDAAW,OAAO;0DAC5D,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,gBAAgB,mBACf,6LAAC,oIAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,WAAU;sEAET,gBAAgB,IAAI,OAAO;;;;;;;;;;;;;;;;;0DAKpC,6LAAC,yIAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzB;GArgBgB;;QACQ,iIAAA,CAAA,iBAAc;QACnB,qIAAA,CAAA,cAAW;QA4KF,kIAAA,CAAA,UAAO;;;KA9KnB;uCAugBD", "debugId": null}}, {"offset": {"line": 5685, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/dashboard/admin/page-builder/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { UserRole } from '@/types/auth'\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute'\nimport { PageBuilder } from '@/components/admin/PageBuilder'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Navigation } from '@/components/Navigation'\nimport { ArrowLeft, Sparkles, FileText, Upload, Zap, Wand2, Layout } from 'lucide-react'\nimport Link from 'next/link'\nimport { PageProject } from '@/types/page-builder'\nimport { toast } from 'sonner'\n\nexport default function PageBuilderPage() {\n  const { user, profile } = useAuth()\n  const [projects, setProjects] = useState<PageProject[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showBuilder, setShowBuilder] = useState(false)\n  const [currentProject, setCurrentProject] = useState<PageProject | undefined>()\n  const [stats, setStats] = useState<any>({})\n\n  // جلب المشاريع\n  const fetchProjects = async () => {\n    try {\n      setLoading(true)\n      // محاكاة جلب البيانات\n      const mockProjects: PageProject[] = []\n      setProjects(mockProjects)\n      \n      // حساب الإحصائيات\n      const totalProjects = mockProjects.length\n      const publishedProjects = mockProjects.filter(p => p.isPublished).length\n      const activeProjects = mockProjects.filter(p => !p.isPublished).length\n      const generatedPages = mockProjects.filter(p => p.generationMode === 'ai').length\n      \n      setStats({\n        totalProjects,\n        publishedProjects,\n        activeProjects,\n        generatedPages\n      })\n    } catch (error) {\n      console.error('Error fetching projects:', error)\n      toast.error('خطأ في جلب المشاريع')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  useEffect(() => {\n    fetchProjects()\n  }, [])\n\n  // حفظ مشروع\n  const saveProject = async (project: PageProject) => {\n    try {\n      toast.success('تم حفظ المشروع بنجاح')\n      fetchProjects()\n    } catch (error) {\n      toast.error('فشل في حفظ المشروع')\n    }\n  }\n\n  // معاينة مشروع\n  const handlePreview = (project: PageProject) => {\n    toast.info('معاينة المشروع')\n  }\n\n  // نشر مشروع\n  const publishProject = async (project: PageProject) => {\n    try {\n      toast.success('تم نشر المشروع بنجاح')\n      fetchProjects()\n    } catch (error) {\n      toast.error('فشل في نشر المشروع')\n    }\n  }\n\n  if (showBuilder) {\n    return (\n      <ProtectedRoute requiredRole={UserRole.ADMIN}>\n        <div className=\"h-screen flex flex-col\">\n          <div className=\"border-b p-4 bg-background\">\n            <Button \n              variant=\"outline\" \n              onClick={() => setShowBuilder(false)}\n            >\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\n              العودة للمشاريع\n            </Button>\n          </div>\n          <div className=\"flex-1\">\n            <PageBuilder\n              project={currentProject}\n              onSave={saveProject}\n              onPreview={handlePreview}\n              onPublish={publishProject}\n            />\n          </div>\n        </div>\n      </ProtectedRoute>\n    )\n  }\n\n  return (\n    <ProtectedRoute requiredRole={UserRole.ADMIN}>\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900\">\n        <Navigation />\n        \n        <main className=\"container mx-auto px-4 py-8\">\n          {/* Header */}\n          <div className=\"mb-8\">\n            <div className=\"flex items-center gap-4 mb-4\">\n              <Button variant=\"outline\" size=\"sm\" asChild>\n                <Link href=\"/dashboard/admin\">\n                  <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                  العودة للوحة التحكم\n                </Link>\n              </Button>\n            </div>\n            \n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white arabic-text flex items-center gap-2\">\n                  <Sparkles className=\"h-8 w-8 text-purple-500\" />\n                  بناء الصفحات الذكية\n                </h1>\n                <p className=\"text-gray-600 dark:text-gray-300 mt-2 arabic-text\">\n                  إنشاء صفحات احترافية باستخدام الذكاء الاصطناعي والقوالب الجاهزة\n                </p>\n              </div>\n            </div>\n          </div>\n        \n          {/* الإحصائيات السريعة */}\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-muted-foreground\">إجمالي المشاريع</p>\n                    <p className=\"text-2xl font-bold\">{stats.totalProjects || 0}</p>\n                  </div>\n                  <FileText className=\"h-8 w-8 text-blue-500\" />\n                </div>\n              </CardContent>\n            </Card>\n            \n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-muted-foreground\">المشاريع المنشورة</p>\n                    <p className=\"text-2xl font-bold\">{stats.publishedProjects || 0}</p>\n                  </div>\n                  <Upload className=\"h-8 w-8 text-green-500\" />\n                </div>\n              </CardContent>\n            </Card>\n            \n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-muted-foreground\">المشاريع النشطة</p>\n                    <p className=\"text-2xl font-bold\">{stats.activeProjects || 0}</p>\n                  </div>\n                  <Zap className=\"h-8 w-8 text-yellow-500\" />\n                </div>\n              </CardContent>\n            </Card>\n            \n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-muted-foreground\">الصفحات المولدة</p>\n                    <p className=\"text-2xl font-bold\">{stats.generatedPages || 0}</p>\n                  </div>\n                  <Sparkles className=\"h-8 w-8 text-purple-500\" />\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* أزرار الإجراءات الرئيسية */}\n          <div className=\"flex items-center justify-between mb-8\">\n            <div className=\"flex gap-3\">\n              <Button onClick={() => setShowBuilder(true)} className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\">\n                <Wand2 className=\"h-4 w-4 mr-2\" />\n                إنشاء صفحة جديدة\n              </Button>\n              <Button variant=\"outline\" onClick={() => setShowBuilder(true)}>\n                <Layout className=\"h-4 w-4 mr-2\" />\n                استخدام قالب\n              </Button>\n            </div>\n          </div>\n\n          {/* محتوى الصفحة */}\n          <div className=\"text-center py-12\">\n            <Sparkles className=\"h-16 w-16 mx-auto mb-4 text-muted-foreground\" />\n            <h3 className=\"text-xl font-semibold mb-2\">مرحباً ببناء الصفحات الذكية</h3>\n            <p className=\"text-muted-foreground mb-6\">\n              ابدأ بإنشاء صفحة جديدة باستخدام الذكاء الاصطناعي أو اختر قالباً جاهزاً\n            </p>\n            <div className=\"flex gap-2 justify-center\">\n              <Button onClick={() => setShowBuilder(true)}>\n                <Wand2 className=\"h-4 w-4 mr-2\" />\n                توليد بالذكاء الاصطناعي\n              </Button>\n              <Button variant=\"outline\" onClick={() => setShowBuilder(true)}>\n                <Layout className=\"h-4 w-4 mr-2\" />\n                اختيار قالب\n              </Button>\n            </div>\n          </div>\n        </main>\n      </div>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;;;AAdA;;;;;;;;;;;;AAgBe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IAEzC,eAAe;IACf,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,sBAAsB;YACtB,MAAM,eAA8B,EAAE;YACtC,YAAY;YAEZ,kBAAkB;YAClB,MAAM,gBAAgB,aAAa,MAAM;YACzC,MAAM,oBAAoB,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;YACxE,MAAM,iBAAiB,aAAa,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,WAAW,EAAE,MAAM;YACtE,MAAM,iBAAiB,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,KAAK,MAAM,MAAM;YAEjF,SAAS;gBACP;gBACA;gBACA;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG,EAAE;IAEL,YAAY;IACZ,MAAM,cAAc,OAAO;QACzB,IAAI;YACF,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,eAAe;IACf,MAAM,gBAAgB,CAAC;QACrB,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;IACb;IAEA,YAAY;IACZ,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,IAAI,aAAa;QACf,qBACE,6LAAC,+IAAA,CAAA,iBAAc;YAAC,cAAc,uHAAA,CAAA,WAAQ,CAAC,KAAK;sBAC1C,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,eAAe;;8CAE9B,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAI1C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6IAAA,CAAA,cAAW;4BACV,SAAS;4BACT,QAAQ;4BACR,WAAW;4BACX,WAAW;;;;;;;;;;;;;;;;;;;;;;IAMvB;IAEA,qBACE,6LAAC,+IAAA,CAAA,iBAAc;QAAC,cAAc,uHAAA,CAAA,WAAQ,CAAC,KAAK;kBAC1C,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,mIAAA,CAAA,aAAU;;;;;8BAEX,6LAAC;oBAAK,WAAU;;sCAEd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,OAAO;kDACzC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;;8DACT,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;8CAM5C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAA4B;;;;;;;0DAGlD,6LAAC;gDAAE,WAAU;0DAAoD;;;;;;;;;;;;;;;;;;;;;;;sCAQvE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,OAAI;8CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAA4C;;;;;;sEACzD,6LAAC;4DAAE,WAAU;sEAAsB,MAAM,aAAa,IAAI;;;;;;;;;;;;8DAE5D,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAK1B,6LAAC,mIAAA,CAAA,OAAI;8CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAA4C;;;;;;sEACzD,6LAAC;4DAAE,WAAU;sEAAsB,MAAM,iBAAiB,IAAI;;;;;;;;;;;;8DAEhE,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAKxB,6LAAC,mIAAA,CAAA,OAAI;8CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAA4C;;;;;;sEACzD,6LAAC;4DAAE,WAAU;sEAAsB,MAAM,cAAc,IAAI;;;;;;;;;;;;8DAE7D,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAKrB,6LAAC,mIAAA,CAAA,OAAI;8CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAA4C;;;;;;sEACzD,6LAAC;4DAAE,WAAU;sEAAsB,MAAM,cAAc,IAAI;;;;;;;;;;;;8DAE7D,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,eAAe;wCAAO,WAAU;;0DACrD,6LAAC,kNAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS,IAAM,eAAe;;0DACtD,6LAAC,wNAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;sCAOzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS,IAAM,eAAe;;8DACpC,6LAAC,kNAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS,IAAM,eAAe;;8DACtD,6LAAC,wNAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD;GAhNwB;;QACI,kIAAA,CAAA,UAAO;;;KADX", "debugId": null}}]}