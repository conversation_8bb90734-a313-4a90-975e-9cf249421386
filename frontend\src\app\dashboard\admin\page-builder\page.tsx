'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { UserRole } from '@/types/auth'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { PageBuilder } from '@/components/admin/PageBuilder'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Navigation } from '@/components/Navigation'
import { ArrowLeft, Sparkles, FileText, Upload, Zap, Wand2, Layout } from 'lucide-react'
import Link from 'next/link'
import { PageProject } from '@/types/page-builder'
import { toast } from 'sonner'

export default function PageBuilderPage() {
  const { user, profile } = useAuth()
  const [projects, setProjects] = useState<PageProject[]>([])
  const [loading, setLoading] = useState(true)
  const [showBuilder, setShowBuilder] = useState(false)
  const [currentProject, setCurrentProject] = useState<PageProject | undefined>()
  const [stats, setStats] = useState<any>({})

  // جلب المشاريع
  const fetchProjects = async () => {
    try {
      setLoading(true)
      // محاكاة جلب البيانات
      const mockProjects: PageProject[] = []
      setProjects(mockProjects)
      
      // حساب الإحصائيات
      const totalProjects = mockProjects.length
      const publishedProjects = mockProjects.filter(p => p.isPublished).length
      const activeProjects = mockProjects.filter(p => !p.isPublished).length
      const generatedPages = mockProjects.filter(p => p.generationMode === 'ai').length
      
      setStats({
        totalProjects,
        publishedProjects,
        activeProjects,
        generatedPages
      })
    } catch (error) {
      console.error('Error fetching projects:', error)
      toast.error('خطأ في جلب المشاريع')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProjects()
  }, [])

  // حفظ مشروع
  const saveProject = async (project: PageProject) => {
    try {
      toast.success('تم حفظ المشروع بنجاح')
      fetchProjects()
    } catch (error) {
      toast.error('فشل في حفظ المشروع')
    }
  }

  // معاينة مشروع
  const handlePreview = (project: PageProject) => {
    toast.info('معاينة المشروع')
  }

  // نشر مشروع
  const publishProject = async (project: PageProject) => {
    try {
      toast.success('تم نشر المشروع بنجاح')
      fetchProjects()
    } catch (error) {
      toast.error('فشل في نشر المشروع')
    }
  }

  if (showBuilder) {
    return (
      <ProtectedRoute requiredRole={UserRole.ADMIN}>
        <div className="h-screen flex flex-col">
          <div className="border-b p-4 bg-background">
            <Button 
              variant="outline" 
              onClick={() => setShowBuilder(false)}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              العودة للمشاريع
            </Button>
          </div>
          <div className="flex-1">
            <PageBuilder
              project={currentProject}
              onSave={saveProject}
              onPreview={handlePreview}
              onPublish={publishProject}
            />
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute requiredRole={UserRole.ADMIN}>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <Navigation />
        
        <main className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center gap-4 mb-4">
              <Button variant="outline" size="sm" asChild>
                <Link href="/dashboard/admin">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  العودة للوحة التحكم
                </Link>
              </Button>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text flex items-center gap-2">
                  <Sparkles className="h-8 w-8 text-purple-500" />
                  بناء الصفحات الذكية
                </h1>
                <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
                  إنشاء صفحات احترافية باستخدام الذكاء الاصطناعي والقوالب الجاهزة
                </p>
              </div>
            </div>
          </div>
        
          {/* الإحصائيات السريعة */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">إجمالي المشاريع</p>
                    <p className="text-2xl font-bold">{stats.totalProjects || 0}</p>
                  </div>
                  <FileText className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">المشاريع المنشورة</p>
                    <p className="text-2xl font-bold">{stats.publishedProjects || 0}</p>
                  </div>
                  <Upload className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">المشاريع النشطة</p>
                    <p className="text-2xl font-bold">{stats.activeProjects || 0}</p>
                  </div>
                  <Zap className="h-8 w-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">الصفحات المولدة</p>
                    <p className="text-2xl font-bold">{stats.generatedPages || 0}</p>
                  </div>
                  <Sparkles className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* أزرار الإجراءات الرئيسية */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex gap-3">
              <Button onClick={() => setShowBuilder(true)} className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                <Wand2 className="h-4 w-4 mr-2" />
                إنشاء صفحة جديدة
              </Button>
              <Button variant="outline" onClick={() => setShowBuilder(true)}>
                <Layout className="h-4 w-4 mr-2" />
                استخدام قالب
              </Button>
            </div>
          </div>

          {/* محتوى الصفحة */}
          <div className="text-center py-12">
            <Sparkles className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-xl font-semibold mb-2">مرحباً ببناء الصفحات الذكية</h3>
            <p className="text-muted-foreground mb-6">
              ابدأ بإنشاء صفحة جديدة باستخدام الذكاء الاصطناعي أو اختر قالباً جاهزاً
            </p>
            <div className="flex gap-2 justify-center">
              <Button onClick={() => setShowBuilder(true)}>
                <Wand2 className="h-4 w-4 mr-2" />
                توليد بالذكاء الاصطناعي
              </Button>
              <Button variant="outline" onClick={() => setShowBuilder(true)}>
                <Layout className="h-4 w-4 mr-2" />
                اختيار قالب
              </Button>
            </div>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  )
}
