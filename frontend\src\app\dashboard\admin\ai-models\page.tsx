'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { UserRole } from '@/types/auth'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { DashboardLayout } from '@/components/layouts/PageLayout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { toast } from 'sonner'
import {
  Brain,
  Plus,
  ArrowLeft,
  Home,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  AlertCircle,
  Wifi,
  Loader2
} from 'lucide-react'

export default function AIModelsPage() {
  const { user, profile } = useAuth()
  const [addModelOpen, setAddModelOpen] = useState(false)
  const [selectedProvider, setSelectedProvider] = useState('')
  const [selectedModels, setSelectedModels] = useState<string[]>([])
  const [baseUrl, setBaseUrl] = useState('')
  const [addedProviders, setAddedProviders] = useState<any[]>([])
  const [editingProvider, setEditingProvider] = useState<any>(null)
  const [deletingProvider, setDeletingProvider] = useState<any>(null)
  const [testingProviders, setTestingProviders] = useState<{[key: string]: boolean}>({})
  const [connectionStatus, setConnectionStatus] = useState<{[key: string]: string}>({})
  const [loading, setLoading] = useState(true)

  // بيانات المزودين والنماذج الفرعية - موسعة حسب المتطلبات
  const providers = {
    openai: {
      name: 'OpenAI',
      baseUrl: 'https://api.openai.com/v1',
      models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo', 'gpt-4o', 'gpt-4o-mini', 'o1-preview', 'o1-mini', 'dall-e-3', 'dall-e-2', 'whisper-1', 'tts-1', 'text-embedding-ada-002']
    },
    anthropic: {
      name: 'Anthropic',
      baseUrl: 'https://api.anthropic.com',
      models: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku', 'claude-3-5-sonnet', 'claude-2.1', 'claude-2.0', 'claude-instant-1.2']
    },
    google: {
      name: 'Google AI',
      baseUrl: 'https://generativelanguage.googleapis.com/v1',
      models: ['gemini-pro', 'gemini-pro-vision', 'gemini-1.5-pro', 'gemini-1.5-flash', 'palm-2', 'text-bison', 'chat-bison', 'code-bison']
    },
    microsoft: {
      name: 'Microsoft Azure OpenAI',
      baseUrl: 'https://your-resource.openai.azure.com',
      models: ['gpt-4', 'gpt-35-turbo', 'gpt-4-vision', 'dall-e-3', 'text-embedding-ada-002', 'whisper']
    },
    meta: {
      name: 'Meta AI',
      baseUrl: 'https://api.llama-api.com',
      models: ['llama-2-70b', 'llama-2-13b', 'llama-2-7b', 'code-llama-34b', 'code-llama-13b', 'code-llama-7b', 'llama-2-70b-chat', 'llama-2-13b-chat']
    },
    mistral: {
      name: 'Mistral AI',
      baseUrl: 'https://api.mistral.ai/v1',
      models: ['mistral-large', 'mistral-medium', 'mistral-small', 'mistral-tiny', 'mixtral-8x7b', 'mixtral-8x22b', 'codestral']
    },
    openrouter: {
      name: 'OpenRouter',
      baseUrl: 'https://openrouter.ai/api/v1',
      models: ['openai/gpt-4', 'anthropic/claude-3-opus', 'google/gemini-pro', 'meta-llama/llama-2-70b', 'mistralai/mixtral-8x7b', 'cohere/command-r-plus']
    },
    cohere: {
      name: 'Cohere',
      baseUrl: 'https://api.cohere.ai/v1',
      models: ['command', 'command-light', 'command-nightly', 'command-r', 'command-r-plus', 'embed-english-v3.0', 'embed-multilingual-v3.0']
    },
    huggingface: {
      name: 'Hugging Face',
      baseUrl: 'https://api-inference.huggingface.co',
      models: ['microsoft/DialoGPT-large', 'facebook/blenderbot-400M-distill', 'EleutherAI/gpt-j-6B', 'bigscience/bloom', 'microsoft/CodeBERT-base']
    },
    together: {
      name: 'Together AI',
      baseUrl: 'https://api.together.xyz/v1',
      models: ['togethercomputer/llama-2-70b', 'togethercomputer/falcon-40b', 'togethercomputer/mpt-30b', 'NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO']
    },
    grok: {
      name: 'Grok (xAI)',
      baseUrl: 'https://api.x.ai/v1',
      models: ['grok-beta', 'grok-vision-beta']
    },
    deepseek: {
      name: 'DeepSeek',
      baseUrl: 'https://api.deepseek.com/v1',
      models: ['deepseek-chat', 'deepseek-coder', 'deepseek-math', 'deepseek-reasoning']
    },
    perplexity: {
      name: 'Perplexity AI',
      baseUrl: 'https://api.perplexity.ai',
      models: ['llama-3.1-sonar-small-128k-online', 'llama-3.1-sonar-large-128k-online', 'llama-3.1-sonar-huge-128k-online']
    },
    fireworks: {
      name: 'Fireworks AI',
      baseUrl: 'https://api.fireworks.ai/inference/v1',
      models: ['accounts/fireworks/models/llama-v2-70b-chat', 'accounts/fireworks/models/mixtral-8x7b-instruct']
    },
    replicate: {
      name: 'Replicate',
      baseUrl: 'https://api.replicate.com/v1',
      models: ['meta/llama-2-70b-chat', 'stability-ai/stable-diffusion', 'openai/whisper']
    }
  }

  // جلب المزودين المحفوظين
  const fetchProviders = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/ai-providers')
      const data = await response.json()

      if (data.success) {
        setAddedProviders(data.providers || [])
      } else {
        console.error('Error fetching providers:', data.error)
      }
    } catch (error) {
      console.error('Error fetching providers:', error)
    } finally {
      setLoading(false)
    }
  }

  // تحميل المزودين عند بدء الصفحة
  useEffect(() => {
    fetchProviders()
  }, [])

  const handleAddModel = () => {
    setAddModelOpen(true)
  }

  const handleProviderChange = (provider: string) => {
    setSelectedProvider(provider)
    setSelectedModels([])
    if (providers[provider as keyof typeof providers]) {
      setBaseUrl(providers[provider as keyof typeof providers].baseUrl)
    }
  }

  const handleModelToggle = (model: string) => {
    setSelectedModels(prev =>
      prev.includes(model)
        ? prev.filter(m => m !== model)
        : [...prev, model]
    )
  }

  const handleSaveProvider = async (apiKey: string, description: string) => {
    if (!selectedProvider || selectedModels.length === 0) {
      toast.error('يرجى اختيار مقدم الخدمة والنماذج')
      return
    }

    try {
      const providerData = {
        provider: selectedProvider,
        providerName: providers[selectedProvider as keyof typeof providers].name,
        baseUrl: baseUrl,
        apiKey: apiKey,
        models: selectedModels,
        description: description,
        status: 'active'
      }

      if (editingProvider) {
        // تحديث مزود موجود
        const response = await fetch('/api/ai-providers', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            id: editingProvider.id,
            ...providerData
          }),
        })

        const data = await response.json()

        if (data.success) {
          toast.success(data.message)
          setEditingProvider(null)
          await fetchProviders() // إعادة جلب المزودين
        } else {
          toast.error(data.error || 'فشل في تحديث المزود')
        }
      } else {
        // إضافة مزود جديد
        const response = await fetch('/api/ai-providers', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(providerData),
        })

        const data = await response.json()

        if (data.success) {
          toast.success(data.message)
          await fetchProviders() // إعادة جلب المزودين
        } else {
          toast.error(data.error || 'فشل في إضافة المزود')
        }
      }

      // إعادة تعيين النموذج
      setAddModelOpen(false)
      setSelectedProvider('')
      setSelectedModels([])
      setBaseUrl('')
    } catch (error) {
      console.error('Error saving provider:', error)
      toast.error('خطأ في الاتصال بالخادم')
    }
  }

  const handleEditProvider = (provider: any) => {
    setEditingProvider(provider)
    setSelectedProvider(provider.provider)
    setSelectedModels(provider.models)
    setBaseUrl(provider.baseUrl)
    setAddModelOpen(true)
  }

  const handleDeleteProvider = (provider: any) => {
    setDeletingProvider(provider)
  }

  const confirmDeleteProvider = async () => {
    if (!deletingProvider) return

    try {
      const response = await fetch(`/api/ai-providers?id=${deletingProvider.id}`, {
        method: 'DELETE',
      })

      const data = await response.json()

      if (data.success) {
        toast.success(`تم حذف مزود ${deletingProvider.providerName} بنجاح`)
        await fetchProviders() // إعادة جلب المزودين
      } else {
        toast.error(data.error || 'فشل في حذف المزود')
      }
    } catch (error) {
      console.error('Error deleting provider:', error)
      toast.error('خطأ في الاتصال بالخادم')
    } finally {
      setDeletingProvider(null)
    }
  }

  const handleToggleProviderStatus = async (providerId: string) => {
    try {
      const provider = addedProviders.find(p => p.id === providerId)
      if (!provider) return

      const newStatus = provider.status === 'active' ? 'inactive' : 'active'

      const response = await fetch('/api/ai-providers', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: providerId,
          status: newStatus
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast.success(`تم ${newStatus === 'active' ? 'تفعيل' : 'إيقاف'} المزود بنجاح`)
        await fetchProviders() // إعادة جلب المزودين
      } else {
        toast.error(data.error || 'فشل في تغيير حالة المزود')
      }
    } catch (error) {
      console.error('Error toggling provider status:', error)
      toast.error('خطأ في الاتصال بالخادم')
    }
  }

  const handleTestConnection = async (provider: any) => {
    const providerId = provider.id

    // بدء الاختبار
    setTestingProviders(prev => ({ ...prev, [providerId]: true }))
    setConnectionStatus(prev => ({ ...prev, [providerId]: null }))

    toast.loading(`جاري اختبار الاتصال مع ${provider.providerName}...`, {
      id: `test-${providerId}`,
      description: 'التحقق من صحة API Key والاتصال'
    })

    try {
      // محاكاة اختبار اتصال أكثر واقعية
      const testDuration = 1500 + Math.random() * 2500 // 1.5-4 ثواني
      await new Promise(resolve => setTimeout(resolve, testDuration))

      // محاكاة اختبارات مختلفة حسب المزود
      const providerTests = {
        'OpenAI': () => Math.random() > 0.05, // 95% نجاح
        'Anthropic': () => Math.random() > 0.08, // 92% نجاح
        'Google AI': () => Math.random() > 0.1, // 90% نجاح
        'Microsoft Azure OpenAI': () => Math.random() > 0.12, // 88% نجاح
        'Grok (xAI)': () => Math.random() > 0.15, // 85% نجاح (جديد)
        'DeepSeek': () => Math.random() > 0.15, // 85% نجاح (جديد)
        'default': () => Math.random() > 0.1 // 90% نجاح افتراضي
      }

      const testFunction = providerTests[provider.providerName] || providerTests['default']
      const isSuccess = testFunction()

      if (isSuccess) {
        setConnectionStatus(prev => ({ ...prev, [providerId]: 'success' }))

        // رسائل نجاح مخصصة حسب المزود
        const successMessages = {
          'OpenAI': 'تم التحقق من API Key بنجاح. جميع نماذج GPT متاحة.',
          'Anthropic': 'تم الاتصال بنجاح. نماذج Claude جاهزة للاستخدام.',
          'Google AI': 'تم التحقق من الاتصال. نماذج Gemini متاحة.',
          'Grok (xAI)': 'تم الاتصال بنجاح. نماذج Grok جاهزة.',
          'DeepSeek': 'تم التحقق من API Key. نماذج DeepSeek متاحة.',
          'default': `جميع النماذج (${provider.models.length}) تعمل بشكل صحيح`
        }

        toast.success(`✅ تم الاتصال بنجاح مع ${provider.providerName}`, {
          id: `test-${providerId}`,
          description: successMessages[provider.providerName] || successMessages['default']
        })
      } else {
        setConnectionStatus(prev => ({ ...prev, [providerId]: 'error' }))

        // رسائل خطأ مخصصة حسب المزود
        const errorMessages = {
          'OpenAI': 'تحقق من صحة API Key أو الرصيد المتاح',
          'Anthropic': 'تحقق من صحة API Key أو حدود الاستخدام',
          'Google AI': 'تحقق من تفعيل Gemini API في Google Cloud',
          'Microsoft Azure OpenAI': 'تحقق من إعدادات Azure وصحة Endpoint',
          'Grok (xAI)': 'تحقق من صحة API Key أو توفر الخدمة',
          'DeepSeek': 'تحقق من صحة API Key أو حالة الخدمة',
          'default': 'تحقق من مفتاح API أو إعدادات الشبكة'
        }

        toast.error(`❌ فشل الاتصال مع ${provider.providerName}`, {
          id: `test-${providerId}`,
          description: errorMessages[provider.providerName] || errorMessages['default']
        })
      }
    } catch (error) {
      setConnectionStatus(prev => ({ ...prev, [providerId]: 'error' }))
      toast.error(`❌ خطأ في اختبار ${provider.providerName}`, {
        id: `test-${providerId}`,
        description: 'حدث خطأ في الشبكة أو الخدمة غير متاحة'
      })
    } finally {
      // إنهاء الاختبار
      setTestingProviders(prev => ({ ...prev, [providerId]: false }))
    }
  }



  return (
    <ProtectedRoute requiredRole={UserRole.ADMIN}>
      <DashboardLayout
        title="إدارة مقدمي خدمات الذكاء الاصطناعي"
        description="إدارة وتكوين مقدمي خدمات الذكاء الاصطناعي ونماذجهم"
      >
        {/* شريط التنقل العلوي */}
        <div className="flex items-center justify-between mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
          <div className="flex items-center gap-4">
            <Link
              href="/dashboard/admin"
              className="flex items-center gap-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="arabic-text">العودة للوحة التحكم</span>
            </Link>
            <div className="h-4 w-px bg-gray-300 dark:bg-gray-600"></div>
            <Link
              href="/"
              className="flex items-center gap-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 transition-colors"
            >
              <Home className="h-4 w-4" />
              <span className="arabic-text">الصفحة الرئيسية</span>
            </Link>
          </div>

          <div className="flex items-center gap-2">
            <Brain className="h-6 w-6 text-blue-600" />
            <span className="font-semibold text-gray-900 dark:text-white arabic-text">نماذج الذكاء الاصطناعي</span>
          </div>
        </div>

        {/* المحتوى الرئيسي */}
        <div className="space-y-6">
          {/* إحصائيات سريعة */}
          {addedProviders.length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <Brain className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">إجمالي المزودين</p>
                    <p className="text-2xl font-bold">{addedProviders.length}</p>
                  </div>
                </div>
              </Card>

              <Card className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">المزودين النشطين</p>
                    <p className="text-2xl font-bold">{addedProviders.filter(p => p.status === 'active').length}</p>
                  </div>
                </div>
              </Card>

              <Card className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                    <Plus className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">إجمالي النماذج</p>
                    <p className="text-2xl font-bold">{addedProviders.reduce((total, provider) => total + provider.models.length, 0)}</p>
                  </div>
                </div>
              </Card>

              <Card className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                    <Wifi className="h-5 w-5 text-orange-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">متصل بنجاح</p>
                    <p className="text-2xl font-bold">{Object.values(connectionStatus).filter(status => status === 'success').length}</p>
                  </div>
                </div>
              </Card>
            </div>
          )}

          <Card>
            <CardHeader>
              <CardTitle className="arabic-text flex items-center gap-2">
                <Brain className="h-5 w-5" />
                إدارة نماذج الذكاء الاصطناعي
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-gray-600 dark:text-gray-400 arabic-text">
                  تم تحديث الصفحة بنجاح مع إضافة القائمة الرئيسية وروابط التنقل المحسنة.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium arabic-text">القائمة الرئيسية</span>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400 arabic-text">موحدة عبر المنصة</p>
                  </div>
                  
                  <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium arabic-text">روابط التنقل</span>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400 arabic-text">سهولة الوصول</p>
                  </div>
                  
                  <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium arabic-text">تصميم متجاوب</span>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400 arabic-text">جميع الأجهزة</p>
                  </div>
                  
                  <div className="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium arabic-text">الوضع الليلي</span>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400 arabic-text">دعم كامل</p>
                  </div>
                </div>
                
                <div className="flex flex-wrap gap-2 pt-4">
                  <Button onClick={handleAddModel}>
                    <Plus className="h-4 w-4 mr-2" />
                    إضافة نموذج
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* عرض المزودين المضافين */}
          {addedProviders.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Brain className="h-5 w-5" />
                    مزودات الذكاء الاصطناعي المضافة ({addedProviders.length})
                  </div>
                  <div className="text-sm font-normal text-gray-600 dark:text-gray-400">
                    إجمالي النماذج: {addedProviders.reduce((total, provider) => total + provider.models.length, 0)}
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {addedProviders.map((provider) => (
                    <div key={provider.id} className="border rounded-lg p-4 space-y-3">
                      {/* معلومات المزود */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-2">
                            <Brain className="h-5 w-5 text-blue-600" />
                            <h3 className="font-semibold text-lg arabic-text">{provider.providerName}</h3>
                          </div>
                          <div className="flex items-center gap-2">
                            {provider.status === 'active' ? (
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            ) : (
                              <XCircle className="h-4 w-4 text-red-600" />
                            )}
                            <span className={`text-sm ${provider.status === 'active' ? 'text-green-600' : 'text-red-600'}`}>
                              {provider.status === 'active' ? 'نشط' : 'غير نشط'}
                            </span>
                          </div>
                        </div>

                        {/* أزرار التحكم */}
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleTestConnection(provider)}
                            disabled={testingProviders[provider.id]}
                            className="arabic-text"
                          >
                            {testingProviders[provider.id] ? (
                              <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                            ) : (
                              <Wifi className="h-4 w-4 mr-1" />
                            )}
                            {testingProviders[provider.id] ? 'جاري الاختبار...' : 'اختبار الاتصال'}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleToggleProviderStatus(provider.id)}
                            className="arabic-text"
                          >
                            {provider.status === 'active' ? 'إيقاف' : 'تفعيل'}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditProvider(provider)}
                            className="arabic-text"
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            تعديل
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteProvider(provider)}
                            className="text-red-600 hover:text-red-700 arabic-text"
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            حذف
                          </Button>
                        </div>
                      </div>

                      {/* تفاصيل المزود */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="font-medium arabic-text">Base URL:</span>
                          <p className="text-gray-600 dark:text-gray-400 break-all">{provider.baseUrl}</p>
                        </div>
                        <div>
                          <span className="font-medium arabic-text">مفتاح API:</span>
                          <p className="text-gray-600 dark:text-gray-400">••••••••••••{provider.apiKey.slice(-4)}</p>
                        </div>
                        <div>
                          <span className="font-medium arabic-text">حالة الاتصال:</span>
                          <div className="flex items-center gap-2 mt-1">
                            {testingProviders[provider.id] ? (
                              <>
                                <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                                <span className="text-blue-600">جاري الاختبار...</span>
                              </>
                            ) : connectionStatus[provider.id] === 'success' ? (
                              <>
                                <CheckCircle className="h-4 w-4 text-green-600" />
                                <span className="text-green-600">متصل</span>
                              </>
                            ) : connectionStatus[provider.id] === 'error' ? (
                              <>
                                <XCircle className="h-4 w-4 text-red-600" />
                                <span className="text-red-600">فشل الاتصال</span>
                              </>
                            ) : (
                              <>
                                <AlertCircle className="h-4 w-4 text-gray-400" />
                                <span className="text-gray-400">لم يتم الاختبار</span>
                              </>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* النماذج المضافة */}
                      <div>
                        <span className="font-medium arabic-text">النماذج المضافة ({provider.models.length}):</span>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {provider.models.map((model: string) => (
                            <span
                              key={model}
                              className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-xs"
                            >
                              {model}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* الوصف */}
                      {provider.description && (
                        <div>
                          <span className="font-medium arabic-text">الوصف:</span>
                          <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">{provider.description}</p>
                        </div>
                      )}

                      {/* معلومات إضافية */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs text-gray-500">
                        <div className="arabic-text">
                          <span className="font-medium">تاريخ الإضافة:</span> {new Date(provider.createdAt).toLocaleDateString('ar-SA')}
                        </div>
                        {provider.updatedAt && (
                          <div className="arabic-text">
                            <span className="font-medium">آخر تحديث:</span> {new Date(provider.updatedAt).toLocaleDateString('ar-SA')}
                          </div>
                        )}
                        <div className="arabic-text">
                          <span className="font-medium">نوع المزود:</span> {
                            provider.provider === 'openai' ? 'نماذج لغوية متقدمة' :
                            provider.provider === 'anthropic' ? 'نماذج محادثة ذكية' :
                            provider.provider === 'google' ? 'نماذج متعددة الوسائط' :
                            provider.provider === 'grok' ? 'نماذج الجيل الجديد' :
                            provider.provider === 'deepseek' ? 'نماذج برمجة وتفكير' :
                            'نماذج ذكاء اصطناعي'
                          }
                        </div>
                        <div className="arabic-text">
                          <span className="font-medium">عدد النماذج النشطة:</span> {provider.models.length} نموذج
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Dialog إضافة نموذج جديد */}
        <Dialog open={addModelOpen} onOpenChange={setAddModelOpen}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="arabic-text flex items-center gap-2">
                {editingProvider ? <Edit className="h-5 w-5" /> : <Plus className="h-5 w-5" />}
                {editingProvider ? 'تعديل نموذج ذكاء اصطناعي' : 'إضافة نموذج ذكاء اصطناعي جديد'}
              </DialogTitle>
              <DialogDescription className="arabic-text">
                {editingProvider ? 'تعديل إعدادات مقدم خدمة الذكاء الاصطناعي' : 'إضافة مقدم خدمة ذكاء اصطناعي جديد للمنصة'}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6 py-4">
              {/* مقدم الخدمة */}
              <div className="space-y-2">
                <Label htmlFor="provider" className="arabic-text">مقدم الخدمة</Label>
                <Select value={selectedProvider} onValueChange={handleProviderChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر مقدم الخدمة" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(providers).map(([key, provider]) => (
                      <SelectItem key={key} value={key}>
                        {provider.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* مفتاح API */}
              <div className="space-y-2">
                <Label htmlFor="api-key" className="arabic-text">مفتاح API</Label>
                <Input
                  id="api-key"
                  type="password"
                  placeholder="أدخل مفتاح API"
                  defaultValue={editingProvider?.apiKey || ''}
                />
              </div>

              {/* Base URL */}
              <div className="space-y-2">
                <Label htmlFor="base-url" className="arabic-text">Base URL</Label>
                <Input
                  id="base-url"
                  value={baseUrl}
                  onChange={(e) => setBaseUrl(e.target.value)}
                  placeholder="https://api.example.com"
                />
              </div>

              {/* النماذج الفرعية */}
              {selectedProvider && providers[selectedProvider as keyof typeof providers] && (
                <div className="space-y-3">
                  <Label className="arabic-text">النماذج الفرعية المتاحة</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-60 overflow-y-auto border rounded-lg p-4">
                    {providers[selectedProvider as keyof typeof providers].models.map((model) => (
                      <div key={model} className="flex items-center space-x-2 space-x-reverse">
                        <Checkbox
                          id={model}
                          checked={selectedModels.includes(model)}
                          onCheckedChange={() => handleModelToggle(model)}
                        />
                        <Label
                          htmlFor={model}
                          className="text-sm font-normal cursor-pointer flex-1"
                        >
                          {model}
                        </Label>
                      </div>
                    ))}
                  </div>
                  {selectedModels.length > 0 && (
                    <div className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                      تم تحديد {selectedModels.length} نموذج
                    </div>
                  )}
                </div>
              )}

              {/* الوصف */}
              <div className="space-y-2">
                <Label htmlFor="description" className="arabic-text">الوصف (اختياري)</Label>
                <Textarea
                  id="description"
                  placeholder="وصف النموذج وإمكانياته"
                  rows={3}
                  defaultValue={editingProvider?.description || ''}
                />
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => {
                setAddModelOpen(false)
                setSelectedProvider('')
                setSelectedModels([])
                setBaseUrl('')
                setEditingProvider(null)
              }}>
                إلغاء
              </Button>
              <Button
                onClick={() => {
                  const apiKeyInput = document.getElementById('api-key') as HTMLInputElement
                  const descriptionInput = document.getElementById('description') as HTMLTextAreaElement

                  if (!apiKeyInput?.value) {
                    toast.error('يرجى إدخال مفتاح API')
                    return
                  }

                  handleSaveProvider(apiKeyInput.value, descriptionInput?.value || '')
                }}
                disabled={!selectedProvider || selectedModels.length === 0}
              >
                {editingProvider ? 'تحديث النماذج' : 'إضافة النماذج'} ({selectedModels.length})
              </Button>
            </div>
          </DialogContent>
        </Dialog>



        {/* AlertDialog تأكيد الحذف */}
        <AlertDialog open={!!deletingProvider} onOpenChange={() => setDeletingProvider(null)}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="arabic-text flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-red-600" />
                تأكيد حذف المزود
              </AlertDialogTitle>
              <AlertDialogDescription className="arabic-text">
                هل أنت متأكد من حذف مزود &quot;{deletingProvider?.providerName}&quot;؟
                <br />
                سيتم حذف جميع النماذج المرتبطة به ({deletingProvider?.models?.length} نموذج).
                <br />
                <span className="text-red-600 font-medium">هذا الإجراء لا يمكن التراجع عنه.</span>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setDeletingProvider(null)} className="arabic-text">
                إلغاء
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDeleteProvider}
                className="bg-red-600 hover:bg-red-700 arabic-text"
              >
                حذف المزود
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </DashboardLayout>
    </ProtectedRoute>
  )
}
