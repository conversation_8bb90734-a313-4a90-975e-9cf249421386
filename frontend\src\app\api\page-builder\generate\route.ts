import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager } from '@/lib/mockData'
import { AIGenerationRequest, AIGenerationResponse, PageComponent } from '@/types/page-builder'

// POST - توليد صفحة بالذكاء الاصطناعي
export async function POST(request: NextRequest) {
  try {
    const body: AIGenerationRequest = await request.json()
    const {
      prompt,
      language,
      category,
      style,
      colors,
      includeImages,
      includeText,
      pageType,
      targetAudience,
      businessType,
      modelId,
      includeMainHeader,
      mainMenuItems
    } = body

    // التحقق من البيانات المطلوبة
    if (!prompt || !language) {
      return NextResponse.json(
        { error: 'الوصف واللغة مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من النموذج المحدد
    let selectedProvider = null
    let selectedModelName = null

    if (modelId) {
      // تحليل modelId (format: providerId-modelName)
      const modelParts = modelId.split('-')
      const providerId = modelParts[0]
      const modelName = modelParts.slice(1).join('-') // في حالة وجود أكثر من dash في اسم النموذج

      console.log('Looking for provider:', providerId, 'model:', modelName)

      // محاكاة البحث عن المزود (نظراً لأن localStorage لا يعمل في الخادم)
      // سنقبل أي modelId ونعتبره صالحاً للمحاكاة
      selectedProvider = {
        id: providerId,
        providerName: 'Google AI', // افتراضي للمحاكاة
        status: 'active',
        models: [modelName]
      }
      selectedModelName = modelName

      console.log('Selected provider:', selectedProvider)
    } else {
      return NextResponse.json(
        { error: 'لم يتم تحديد نموذج للتوليد' },
        { status: 400 }
      )
    }

    if (!selectedProvider || !selectedModelName) {
      return NextResponse.json(
        { error: 'لا يوجد نموذج متاح للتوليد. يرجى إضافة وتفعيل مزود ذكاء اصطناعي أولاً.' },
        { status: 400 }
      )
    }

    // محاكاة وقت التوليد
    const startTime = Date.now()
    const generationTime = Math.floor(Math.random() * 5000) + 2000 // 2-7 ثواني

    // محاكاة نجاح التوليد بناءً على نوع المزود
    const providerSuccessRates = {
      'OpenAI': 0.95,
      'Anthropic': 0.92,
      'Google AI': 0.90,
      'Microsoft Azure OpenAI': 0.88,
      'Grok (xAI)': 0.85,
      'DeepSeek': 0.85
    }

    const successRate = providerSuccessRates[selectedProvider.providerName as keyof typeof providerSuccessRates] || 0.90
    const success = Math.random() < successRate

    if (!success) {
      return NextResponse.json({
        success: false,
        error: `فشل في توليد الصفحة باستخدام ${selectedProvider.providerName}. يرجى المحاولة مرة أخرى.`,
        metadata: {
          tokensUsed: 0,
          generationTime: Date.now() - startTime,
          modelUsed: `${selectedProvider.providerName} - ${selectedModelName}`
        }
      } as AIGenerationResponse)
    }

    // توليد مكونات الصفحة بناءً على الوصف
    const components = generatePageComponents(prompt, {
      language,
      category,
      style,
      colors,
      includeImages,
      includeText,
      pageType,
      targetAudience,
      businessType,
      includeMainHeader,
      mainMenuItems
    })

    // حساب الرموز المستخدمة (تقدير)
    const tokensUsed = Math.ceil(prompt.length / 4) + Math.floor(Math.random() * 1000) + 500

    // حساب التكلفة
    const cost = selectedModel.subModels.length > 0 
      ? (tokensUsed / 1000) * selectedModel.subModels[0].pricing.outputTokens
      : (tokensUsed / 1000) * 0.002

    // تحديث إحصائيات النموذج
    const models = MockDataManager.getAIModels()
    const modelIndex = models.findIndex(m => m.id === selectedModel!.id)
    if (modelIndex !== -1) {
      const model = models[modelIndex]
      model.usage.totalRequests += 1
      model.usage.totalTokens += tokensUsed
      model.usage.totalCost += cost
      model.usage.lastUsed = new Date().toISOString()
      model.updatedAt = new Date().toISOString()
      
      // تحديث متوسط وقت الاستجابة
      const totalResponseTime = model.usage.averageResponseTime * (model.usage.totalRequests - 1) + generationTime
      model.usage.averageResponseTime = Math.round(totalResponseTime / model.usage.totalRequests)
      
      models[modelIndex] = model
      MockDataManager.saveAIModels(models)
    }

    // إضافة نشاط
    const activities = MockDataManager.getModelActivities()
    activities.push({
      id: MockDataManager.generateId(),
      modelId: `${selectedProvider.id}-${selectedModelName}`,
      type: 'request',
      description: `توليد صفحة: ${prompt.substring(0, 50)}${prompt.length > 50 ? '...' : ''}`,
      details: {
        prompt,
        language,
        category,
        pageType,
        componentsGenerated: components.length,
        provider: selectedProvider.providerName,
        model: selectedModelName
      },
      timestamp: new Date().toISOString(),
      duration: generationTime,
      tokensUsed,
      cost,
      success: true
    })
    MockDataManager.saveModelActivities(activities)

    // إنشاء اقتراحات للتحسين
    const suggestions = generateSuggestions(prompt, components)

    const response: AIGenerationResponse = {
      success: true,
      components,
      suggestions,
      metadata: {
        tokensUsed,
        generationTime,
        modelUsed: `${selectedProvider.providerName} - ${selectedModelName}`,
        componentsCount: components.length,
        estimatedCost: cost
      }
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error generating page:', error)
    return NextResponse.json(
      { error: 'خطأ في توليد الصفحة' },
      { status: 500 }
    )
  }
}

// دالة مساعدة لتوليد مكونات الصفحة
function generatePageComponents(prompt: string, options: any): PageComponent[] {
  const components: PageComponent[] = []

  // تحليل الوصف لتحديد نوع الصفحة والمكونات المطلوبة
  const lowerPrompt = prompt.toLowerCase()

  // إضافة Header الرئيسي إذا كان مطلوباً
  if (options.includeMainHeader && options.mainMenuItems) {
    components.push({
      id: MockDataManager.generateId(),
      type: 'header',
      name: 'Main Header',
      props: {
        content: 'منصة أزياء التخرج',
        menuItems: options.mainMenuItems.slice(0, 6), // أول 6 عناصر
        style: {
          backgroundColor: '#FFFFFF',
          color: '#1F2937',
          borderBottom: '1px solid #E5E7EB',
          padding: '1rem 2rem',
          position: 'sticky',
          top: '0',
          zIndex: '50'
        }
      },
      position: { x: 0, y: 0 },
      size: { width: '100%', height: '80px' },
      isVisible: true
    })
  }

  // إضافة Hero Section دائماً
  const heroYPosition = options.includeMainHeader ? 80 : 0
  components.push({
    id: MockDataManager.generateId(),
    type: 'hero',
    name: 'Hero Section',
    props: {
      content: extractTitle(prompt) || 'مرحباً بكم في موقعنا',
      subtitle: extractSubtitle(prompt) || 'نحن نقدم أفضل الخدمات',
      style: {
        backgroundColor: options.colors?.[0] || '#1F2937',
        color: '#FFFFFF',
        textAlign: 'center',
        padding: '4rem 2rem'
      }
    },
    position: { x: 0, y: heroYPosition },
    size: { width: '100%', height: '500px' },
    isVisible: true
  })

  // حساب الموضع الأساسي للمكونات
  const baseYPosition = heroYPosition + 500
  let currentYPosition = baseYPosition

  // إضافة مكونات بناءً على نوع الصفحة
  if (lowerPrompt.includes('منتج') || lowerPrompt.includes('متجر') || lowerPrompt.includes('تسوق')) {
    // صفحة منتجات
    components.push({
      id: MockDataManager.generateId(),
      type: 'features',
      name: 'Features Section',
      props: {
        content: 'مميزات منتجاتنا',
        style: { padding: '3rem 2rem' }
      },
      position: { x: 0, y: currentYPosition },
      size: { width: '100%', height: '400px' },
      isVisible: true
    })
    currentYPosition += 400

    components.push({
      id: MockDataManager.generateId(),
      type: 'gallery',
      name: 'Product Gallery',
      props: {
        content: 'معرض المنتجات',
        style: { padding: '3rem 2rem' }
      },
      position: { x: 0, y: currentYPosition },
      size: { width: '100%', height: '600px' },
      isVisible: true
    })
    currentYPosition += 600
  }

  if (lowerPrompt.includes('اتصال') || lowerPrompt.includes('تواصل')) {
    // إضافة نموذج اتصال
    components.push({
      id: MockDataManager.generateId(),
      type: 'contact',
      name: 'Contact Form',
      props: {
        content: 'تواصل معنا',
        style: { padding: '3rem 2rem' }
      },
      position: { x: 0, y: currentYPosition },
      size: { width: '100%', height: '500px' },
      isVisible: true
    })
    currentYPosition += 500
  }

  if (lowerPrompt.includes('عن') || lowerPrompt.includes('حول')) {
    // إضافة قسم عن الشركة
    components.push({
      id: MockDataManager.generateId(),
      type: 'text',
      name: 'About Section',
      props: {
        content: extractAboutContent(prompt) || 'نحن شركة رائدة في مجالنا',
        style: { padding: '3rem 2rem' }
      },
      position: { x: 0, y: currentYPosition },
      size: { width: '100%', height: '300px' },
      isVisible: true
    })
    currentYPosition += 300
  }

  if (options.includeImages !== false) {
    // إضافة معرض صور
    components.push({
      id: MockDataManager.generateId(),
      type: 'gallery',
      name: 'Image Gallery',
      props: {
        content: 'معرض الصور',
        style: { padding: '2rem' }
      },
      position: { x: 0, y: currentYPosition },
      size: { width: '100%', height: '400px' },
      isVisible: true
    })
    currentYPosition += 400
  }

  // إضافة Footer دائماً
  components.push({
    id: MockDataManager.generateId(),
    type: 'footer',
    name: 'Footer',
    props: {
      content: 'جميع الحقوق محفوظة',
      style: {
        backgroundColor: '#374151',
        color: '#FFFFFF',
        textAlign: 'center',
        padding: '2rem'
      }
    },
    position: { x: 0, y: currentYPosition },
    size: { width: '100%', height: '200px' },
    isVisible: true
  })

  return components
}

// دالة مساعدة لاستخراج العنوان
function extractTitle(prompt: string): string | null {
  const titlePatterns = [
    /عنوان[:\s]+([^.،]+)/,
    /اسم[:\s]+([^.،]+)/,
    /موقع[:\s]+([^.،]+)/,
    /صفحة[:\s]+([^.،]+)/
  ]

  for (const pattern of titlePatterns) {
    const match = prompt.match(pattern)
    if (match) {
      return match[1].trim()
    }
  }

  return null
}

// دالة مساعدة لاستخراج العنوان الفرعي
function extractSubtitle(prompt: string): string | null {
  const subtitlePatterns = [
    /وصف[:\s]+([^.،]+)/,
    /شعار[:\s]+([^.،]+)/,
    /نبذة[:\s]+([^.،]+)/
  ]

  for (const pattern of subtitlePatterns) {
    const match = prompt.match(pattern)
    if (match) {
      return match[1].trim()
    }
  }

  return null
}

// دالة مساعدة لاستخراج محتوى "عن الشركة"
function extractAboutContent(prompt: string): string | null {
  const aboutPatterns = [
    /عن الشركة[:\s]+([^.]+)/,
    /حول[:\s]+([^.]+)/,
    /نحن[:\s]+([^.]+)/
  ]

  for (const pattern of aboutPatterns) {
    const match = prompt.match(pattern)
    if (match) {
      return match[1].trim()
    }
  }

  return null
}

// دالة مساعدة لتوليد اقتراحات التحسين
function generateSuggestions(prompt: string, components: PageComponent[]): string[] {
  const suggestions = []

  if (components.length < 3) {
    suggestions.push('يمكنك إضافة المزيد من الأقسام لجعل الصفحة أكثر تفصيلاً')
  }

  if (!components.some(c => c.type === 'contact')) {
    suggestions.push('فكر في إضافة نموذج اتصال لتسهيل التواصل مع الزوار')
  }

  if (!components.some(c => c.type === 'testimonial')) {
    suggestions.push('إضافة قسم آراء العملاء يمكن أن يزيد من الثقة')
  }

  if (!components.some(c => c.type === 'gallery')) {
    suggestions.push('معرض الصور يمكن أن يجعل الصفحة أكثر جاذبية')
  }

  suggestions.push('تأكد من تحسين الصفحة للهواتف المحمولة')
  suggestions.push('استخدم ألوان متناسقة مع هوية علامتك التجارية')

  return suggestions
}
