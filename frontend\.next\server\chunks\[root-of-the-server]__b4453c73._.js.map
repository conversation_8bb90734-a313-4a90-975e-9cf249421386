{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/mockData.ts"], "sourcesContent": ["// بيانات وهمية للتطوير والاختبار\nimport { AIModel, AISubModel, ModelActivity } from '@/types/ai-models'\nimport { PageTemplate, PageProject, ComponentLibraryItem, PageComponent } from '@/types/page-builder'\n\nexport interface MockPage {\n  id: string\n  slug: string\n  is_published: boolean\n  author_id: string\n  featured_image?: string\n  created_at: string\n  updated_at: string\n  page_content: MockPageContent[]\n  profiles?: {\n    full_name: string\n  }\n}\n\nexport interface MockPageContent {\n  id: string\n  page_id: string\n  language: 'ar' | 'en' | 'fr'\n  title: string\n  content: string\n  meta_description?: string\n  meta_keywords?: string\n}\n\nexport interface MockMenuItem {\n  id: string\n  title_ar: string\n  title_en?: string\n  title_fr?: string\n  slug: string\n  icon?: string\n  parent_id?: string\n  order_index: number\n  is_active: boolean\n  target_type: 'internal' | 'external' | 'page'\n  target_value: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface MockAIProvider {\n  id: string\n  provider: string\n  providerName: string\n  baseUrl: string\n  apiKey: string\n  models: string[]\n  description?: string\n  status: 'active' | 'inactive'\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface MockCategory {\n  id: string\n  name_ar: string\n  name_en?: string\n  name_fr?: string\n  slug: string\n  icon?: string\n  description?: string\n  is_active: boolean\n  order_index: number\n  created_at: string\n  updated_at: string\n}\n\nexport interface MockProduct {\n  id: string\n  name: string\n  description: string\n  category: string // تغيير من union type إلى string للمرونة\n  price: number\n  rental_price?: number\n  colors: string[]\n  sizes: string[]\n  images: string[]\n  stock_quantity: number\n  is_available: boolean\n  created_at: string\n  updated_at: string\n  rating?: number\n  reviews_count?: number\n  features?: string[]\n  specifications?: Record<string, any>\n}\n\nexport interface MockSchool {\n  id: string\n  admin_id?: string\n  name: string\n  name_en?: string\n  name_fr?: string\n  address?: string\n  city?: string\n  phone?: string\n  email?: string\n  website?: string\n  logo_url?: string\n  graduation_date?: string\n  student_count: number\n  is_active: boolean\n  settings?: Record<string, any>\n  created_at: string\n  updated_at: string\n}\n\nexport interface MockOrder {\n  id: string\n  order_number: string\n  customer_id: string\n  customer_name: string\n  customer_email: string\n  customer_phone?: string\n  status: 'pending' | 'confirmed' | 'in_production' | 'shipped' | 'delivered' | 'cancelled'\n  items: MockOrderItem[]\n  subtotal: number\n  tax: number\n  shipping_cost: number\n  total: number\n  payment_status: 'pending' | 'paid' | 'failed' | 'refunded'\n  payment_method?: string\n  shipping_address: {\n    street: string\n    city: string\n    state: string\n    postal_code: string\n    country: string\n  }\n  tracking_number?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n  delivery_date?: string\n  school_id?: string\n  school_name?: string\n}\n\nexport interface MockOrderItem {\n  id: string\n  order_id: string\n  product_id: string\n  product_name: string\n  product_image: string\n  category: string\n  quantity: number\n  unit_price: number\n  total_price: number\n  customizations?: {\n    color?: string\n    size?: string\n    embroidery?: string\n    special_requests?: string\n  }\n}\n\n// بيانات وهمية للصفحات\nexport const mockPages: MockPage[] = [\n  {\n    id: '1',\n    slug: 'about-us',\n    is_published: true,\n    author_id: 'admin-1',\n    featured_image: '/images/about-hero.jpg',\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-20T14:30:00Z',\n    profiles: {\n      full_name: 'مدير النظام'\n    },\n    page_content: [\n      {\n        id: '1-ar',\n        page_id: '1',\n        language: 'ar',\n        title: 'من نحن',\n        content: '<h2>مرحباً بكم في منصة أزياء التخرج</h2><p>نحن منصة متخصصة في تأجير وبيع أزياء التخرج المغربية الأصيلة. نهدف إلى جعل يوم تخرجكم لا يُنسى من خلال توفير أجمل الأزياء التقليدية.</p><p>تأسست منصتنا عام 2024 بهدف خدمة الطلاب والطالبات في جميع أنحاء المغرب، ونفتخر بتقديم خدمات عالية الجودة وأسعار مناسبة.</p>',\n        meta_description: 'تعرف على منصة أزياء التخرج المغربية - خدمات تأجير وبيع الأزياء التقليدية',\n        meta_keywords: 'أزياء التخرج، المغرب، تأجير، بيع، تقليدي'\n      },\n      {\n        id: '1-en',\n        page_id: '1',\n        language: 'en',\n        title: 'About Us',\n        content: '<h2>Welcome to Graduation Attire Platform</h2><p>We are a specialized platform for renting and selling authentic Moroccan graduation attire. We aim to make your graduation day unforgettable by providing the most beautiful traditional outfits.</p><p>Our platform was founded in 2024 to serve students throughout Morocco, and we pride ourselves on providing high-quality services at affordable prices.</p>',\n        meta_description: 'Learn about the Moroccan Graduation Attire Platform - traditional outfit rental and sales services',\n        meta_keywords: 'graduation attire, Morocco, rental, sales, traditional'\n      }\n    ]\n  },\n  {\n    id: '2',\n    slug: 'services',\n    is_published: true,\n    author_id: 'admin-1',\n    created_at: '2024-01-16T09:00:00Z',\n    updated_at: '2024-01-22T16:45:00Z',\n    profiles: {\n      full_name: 'مدير النظام'\n    },\n    page_content: [\n      {\n        id: '2-ar',\n        page_id: '2',\n        language: 'ar',\n        title: 'خدماتنا',\n        content: '<h2>خدماتنا المتميزة</h2><h3>تأجير الأزياء</h3><p>نوفر خدمة تأجير أزياء التخرج لفترات مرنة مع ضمان النظافة والجودة.</p><h3>بيع الأزياء</h3><p>إمكانية شراء الأزياء للاحتفاظ بها كذكرى جميلة من يوم التخرج.</p><h3>التخصيص</h3><p>خدمات تخصيص الأزياء حسب المقاسات والتفضيلات الشخصية.</p>',\n        meta_description: 'خدمات منصة أزياء التخرج - تأجير وبيع وتخصيص الأزياء التقليدية المغربية',\n        meta_keywords: 'خدمات، تأجير، بيع، تخصيص، أزياء التخرج'\n      }\n    ]\n  },\n  {\n    id: '3',\n    slug: 'contact',\n    is_published: true,\n    author_id: 'admin-1',\n    created_at: '2024-01-17T11:00:00Z',\n    updated_at: '2024-01-17T11:00:00Z',\n    profiles: {\n      full_name: 'مدير النظام'\n    },\n    page_content: [\n      {\n        id: '3-ar',\n        page_id: '3',\n        language: 'ar',\n        title: 'اتصل بنا',\n        content: '<h2>تواصل معنا</h2><p>نحن هنا لخدمتكم في أي وقت. يمكنكم التواصل معنا عبر:</p><ul><li>الهاتف: +212 5XX-XXXXXX</li><li>البريد الإلكتروني: <EMAIL></li><li>العنوان: الدار البيضاء، المغرب</li></ul>',\n        meta_description: 'تواصل مع منصة أزياء التخرج المغربية',\n        meta_keywords: 'اتصال، تواصل، خدمة العملاء'\n      }\n    ]\n  }\n]\n\n// بيانات وهمية للقوائم\nexport const mockMenuItems: MockMenuItem[] = [\n  {\n    id: '1',\n    title_ar: 'الرئيسية',\n    title_en: 'Home',\n    title_fr: 'Accueil',\n    slug: 'home',\n    icon: 'Home',\n    order_index: 1,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/',\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-15T10:00:00Z'\n  },\n  {\n    id: '2',\n    title_ar: 'من نحن',\n    title_en: 'About Us',\n    title_fr: 'À propos',\n    slug: 'about',\n    icon: 'Info',\n    order_index: 2,\n    is_active: true,\n    target_type: 'page',\n    target_value: '1',\n    created_at: '2024-01-15T10:05:00Z',\n    updated_at: '2024-01-15T10:05:00Z'\n  },\n  {\n    id: '3',\n    title_ar: 'خدماتنا',\n    title_en: 'Services',\n    title_fr: 'Services',\n    slug: 'services',\n    icon: 'Settings',\n    order_index: 3,\n    is_active: true,\n    target_type: 'page',\n    target_value: '2',\n    created_at: '2024-01-15T10:10:00Z',\n    updated_at: '2024-01-15T10:10:00Z'\n  },\n  {\n    id: '4',\n    title_ar: 'المنتجات',\n    title_en: 'Products',\n    title_fr: 'Produits',\n    slug: 'products',\n    icon: 'Package',\n    order_index: 4,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/products',\n    created_at: '2024-01-15T10:15:00Z',\n    updated_at: '2024-01-15T10:15:00Z'\n  },\n  {\n    id: '5',\n    title_ar: 'تأجير الأزياء',\n    title_en: 'Rental',\n    title_fr: 'Location',\n    slug: 'rental',\n    parent_id: '4',\n    icon: 'Calendar',\n    order_index: 1,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/products?type=rental',\n    created_at: '2024-01-15T10:20:00Z',\n    updated_at: '2024-01-15T10:20:00Z'\n  },\n  {\n    id: '6',\n    title_ar: 'بيع الأزياء',\n    title_en: 'Sales',\n    title_fr: 'Vente',\n    slug: 'sales',\n    parent_id: '4',\n    icon: 'ShoppingCart',\n    order_index: 2,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/products?type=sale',\n    created_at: '2024-01-15T10:25:00Z',\n    updated_at: '2024-01-15T10:25:00Z'\n  },\n  {\n    id: '7',\n    title_ar: 'الكتالوج',\n    title_en: 'Catalog',\n    title_fr: 'Catalogue',\n    slug: 'catalog',\n    icon: 'Grid3X3',\n    order_index: 5,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/catalog',\n    created_at: '2024-01-15T10:30:00Z',\n    updated_at: '2024-01-15T10:30:00Z'\n  },\n  {\n    id: '8',\n    title_ar: 'اتصل بنا',\n    title_en: 'Contact',\n    title_fr: 'Contact',\n    slug: 'contact',\n    icon: 'Phone',\n    order_index: 6,\n    is_active: true,\n    target_type: 'page',\n    target_value: '3',\n    created_at: '2024-01-15T10:35:00Z',\n    updated_at: '2024-01-15T10:35:00Z'\n  }\n]\n\n// بيانات وهمية للفئات\nexport const mockCategories: MockCategory[] = [\n  {\n    id: '1',\n    name_ar: 'أثواب التخرج',\n    name_en: 'Graduation Gowns',\n    name_fr: 'Robes de Graduation',\n    slug: 'gown',\n    icon: '👘',\n    description: 'أثواب التخرج الأكاديمية التقليدية',\n    is_active: true,\n    order_index: 1,\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-15T10:00:00Z'\n  },\n  {\n    id: '2',\n    name_ar: 'قبعات التخرج',\n    name_en: 'Graduation Caps',\n    name_fr: 'Chapeaux de Graduation',\n    slug: 'cap',\n    icon: '🎩',\n    description: 'قبعات التخرج الأكاديمية',\n    is_active: true,\n    order_index: 2,\n    created_at: '2024-01-15T10:05:00Z',\n    updated_at: '2024-01-15T10:05:00Z'\n  },\n  {\n    id: '3',\n    name_ar: 'شرابات التخرج',\n    name_en: 'Graduation Tassels',\n    name_fr: 'Glands de Graduation',\n    slug: 'tassel',\n    icon: '🏷️',\n    description: 'شرابات التخرج الملونة',\n    is_active: true,\n    order_index: 3,\n    created_at: '2024-01-15T10:10:00Z',\n    updated_at: '2024-01-15T10:10:00Z'\n  },\n  {\n    id: '4',\n    name_ar: 'أوشحة التخرج',\n    name_en: 'Graduation Stoles',\n    name_fr: 'Étoles de Graduation',\n    slug: 'stole',\n    icon: '🧣',\n    description: 'أوشحة التخرج المميزة',\n    is_active: true,\n    order_index: 4,\n    created_at: '2024-01-15T10:15:00Z',\n    updated_at: '2024-01-15T10:15:00Z'\n  },\n  {\n    id: '5',\n    name_ar: 'القلانس الأكاديمية',\n    name_en: 'Academic Hoods',\n    name_fr: 'Capuches Académiques',\n    slug: 'hood',\n    icon: '🎓',\n    description: 'القلانس الأكاديمية للدرجات العليا',\n    is_active: true,\n    order_index: 5,\n    created_at: '2024-01-15T10:20:00Z',\n    updated_at: '2024-01-15T10:20:00Z'\n  }\n]\n\n// بيانات وهمية للمنتجات\nexport const mockProducts: MockProduct[] = [\n  {\n    id: '1',\n    name: 'ثوب التخرج الكلاسيكي',\n    description: 'ثوب تخرج أنيق مصنوع من أجود الخامات، مناسب لجميع المناسبات الأكاديمية',\n    category: 'gown',\n    price: 299.99,\n    rental_price: 99.99,\n    colors: ['أسود', 'أزرق داكن', 'بورجوندي'],\n    sizes: ['S', 'M', 'L', 'XL', 'XXL'],\n    images: ['/images/products/gown-classic-1.jpg', '/images/products/gown-classic-2.jpg'],\n    stock_quantity: 25,\n    is_available: true,\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-20T14:30:00Z',\n    rating: 4.8,\n    reviews_count: 42,\n    features: ['مقاوم للتجاعيد', 'قابل للغسيل', 'خامة عالية الجودة'],\n    specifications: {\n      material: 'بوليستر عالي الجودة',\n      weight: '0.8 كيلو',\n      care: 'غسيل جاف أو غسيل عادي'\n    }\n  },\n  {\n    id: '2',\n    name: 'قبعة التخرج التقليدية',\n    description: 'قبعة تخرج تقليدية مع شرابة ذهبية، رمز الإنجاز الأكاديمي',\n    category: 'cap',\n    price: 79.99,\n    rental_price: 29.99,\n    colors: ['أسود', 'أزرق داكن'],\n    sizes: ['One Size'],\n    images: ['/images/products/cap-traditional-1.jpg'],\n    stock_quantity: 50,\n    is_available: true,\n    created_at: '2024-01-16T09:00:00Z',\n    updated_at: '2024-01-22T16:45:00Z',\n    rating: 4.6,\n    reviews_count: 28,\n    features: ['مقاس واحد يناسب الجميع', 'شرابة ذهبية', 'تصميم تقليدي'],\n    specifications: {\n      material: 'قطن مخلوط',\n      tassel_color: 'ذهبي',\n      adjustable: 'نعم'\n    }\n  },\n  {\n    id: '3',\n    name: 'وشاح التخرج المطرز',\n    description: 'وشاح تخرج مطرز بخيوط ذهبية، يضيف لمسة من الأناقة والتميز',\n    category: 'stole',\n    price: 149.99,\n    rental_price: 49.99,\n    colors: ['أبيض مع ذهبي', 'أزرق مع فضي', 'أحمر مع ذهبي'],\n    sizes: ['One Size'],\n    images: ['/images/products/stole-embroidered-1.jpg', '/images/products/stole-embroidered-2.jpg'],\n    stock_quantity: 15,\n    is_available: true,\n    created_at: '2024-01-17T11:00:00Z',\n    updated_at: '2024-01-25T10:15:00Z',\n    rating: 4.9,\n    reviews_count: 18,\n    features: ['تطريز يدوي', 'خيوط ذهبية', 'تصميم فاخر'],\n    specifications: {\n      material: 'حرير طبيعي',\n      embroidery: 'خيوط ذهبية وفضية',\n      length: '150 سم'\n    }\n  },\n  {\n    id: '4',\n    name: 'شرابة التخرج الذهبية',\n    description: 'شرابة تخرج ذهبية اللون، رمز التفوق والإنجاز الأكاديمي',\n    category: 'tassel',\n    price: 39.99,\n    rental_price: 15.99,\n    colors: ['ذهبي', 'فضي', 'أزرق', 'أحمر'],\n    sizes: ['One Size'],\n    images: ['/images/products/tassel-gold-1.jpg'],\n    stock_quantity: 100,\n    is_available: true,\n    created_at: '2024-01-18T14:00:00Z',\n    updated_at: '2024-01-26T09:30:00Z',\n    rating: 4.7,\n    reviews_count: 35,\n    features: ['خيوط عالية الجودة', 'ألوان ثابتة', 'سهل التركيب'],\n    specifications: {\n      material: 'خيوط حريرية',\n      length: '23 سم',\n      attachment: 'مشبك معدني'\n    }\n  },\n  {\n    id: '5',\n    name: 'قلنسوة الدكتوراه الفاخرة',\n    description: 'قلنسوة دكتوراه فاخرة مصممة خصيصاً لحفلات التخرج الأكاديمية العليا',\n    category: 'hood',\n    price: 199.99,\n    rental_price: 79.99,\n    colors: ['أسود مع ذهبي', 'أزرق مع فضي'],\n    sizes: ['M', 'L', 'XL'],\n    images: ['/images/products/hood-doctorate-1.jpg', '/images/products/hood-doctorate-2.jpg'],\n    stock_quantity: 8,\n    is_available: true,\n    created_at: '2024-01-19T16:00:00Z',\n    updated_at: '2024-01-27T12:00:00Z',\n    rating: 5.0,\n    reviews_count: 12,\n    features: ['تصميم أكاديمي أصيل', 'خامة فاخرة', 'مناسب للدكتوراه'],\n    specifications: {\n      material: 'مخمل عالي الجودة',\n      lining: 'حرير ملون',\n      academic_level: 'دكتوراه'\n    }\n  }\n]\n\n// بيانات وهمية للمدارس\nexport const mockSchools: MockSchool[] = [\n  {\n    id: '1',\n    admin_id: 'admin-school-1',\n    name: 'جامعة الإمارات العربية المتحدة',\n    name_en: 'United Arab Emirates University',\n    name_fr: 'Université des Émirats Arabes Unis',\n    address: 'شارع الجامعة، العين',\n    city: 'العين',\n    phone: '+971-3-713-5000',\n    email: '<EMAIL>',\n    website: 'https://www.uaeu.ac.ae',\n    logo_url: '/images/schools/uaeu-logo.png',\n    graduation_date: '2024-06-15',\n    student_count: 14500,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'قاعة الاحتفالات الكبرى',\n      dress_code: 'formal',\n      photography_allowed: true\n    },\n    created_at: '2024-01-10T08:00:00Z',\n    updated_at: '2024-01-25T10:30:00Z'\n  },\n  {\n    id: '2',\n    admin_id: 'admin-school-2',\n    name: 'الجامعة الأمريكية في الشارقة',\n    name_en: 'American University of Sharjah',\n    name_fr: 'Université Américaine de Sharjah',\n    address: 'شارع الجامعة، الشارقة',\n    city: 'الشارقة',\n    phone: '+971-6-515-5555',\n    email: '<EMAIL>',\n    website: 'https://www.aus.edu',\n    logo_url: '/images/schools/aus-logo.png',\n    graduation_date: '2024-05-20',\n    student_count: 6200,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'مسرح الجامعة',\n      dress_code: 'academic',\n      photography_allowed: true\n    },\n    created_at: '2024-01-12T09:15:00Z',\n    updated_at: '2024-01-28T14:20:00Z'\n  },\n  {\n    id: '3',\n    admin_id: 'admin-school-3',\n    name: 'جامعة زايد',\n    name_en: 'Zayed University',\n    name_fr: 'Université Zayed',\n    address: 'شارع الشيخ زايد، دبي',\n    city: 'دبي',\n    phone: '+971-4-402-1111',\n    email: '<EMAIL>',\n    website: 'https://www.zu.ac.ae',\n    logo_url: '/images/schools/zu-logo.png',\n    graduation_date: '2024-06-10',\n    student_count: 9800,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'مركز المؤتمرات',\n      dress_code: 'formal',\n      photography_allowed: false\n    },\n    created_at: '2024-01-15T11:00:00Z',\n    updated_at: '2024-02-01T16:45:00Z'\n  },\n  {\n    id: '4',\n    admin_id: 'admin-school-4',\n    name: 'كلية الإمارات للتكنولوجيا',\n    name_en: 'Emirates Institute of Technology',\n    name_fr: 'Institut de Technologie des Émirats',\n    address: 'المنطقة الأكاديمية، أبوظبي',\n    city: 'أبوظبي',\n    phone: '+971-2-401-4000',\n    email: '<EMAIL>',\n    website: 'https://www.eit.ac.ae',\n    logo_url: '/images/schools/eit-logo.png',\n    graduation_date: '2024-07-05',\n    student_count: 3500,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'القاعة الرئيسية',\n      dress_code: 'business',\n      photography_allowed: true\n    },\n    created_at: '2024-01-18T13:30:00Z',\n    updated_at: '2024-02-05T09:15:00Z'\n  },\n  {\n    id: '5',\n    admin_id: 'admin-school-5',\n    name: 'معهد أبوظبي للتعليم التقني',\n    name_en: 'Abu Dhabi Technical Institute',\n    name_fr: 'Institut Technique d\\'Abu Dhabi',\n    address: 'المنطقة الصناعية، أبوظبي',\n    city: 'أبوظبي',\n    phone: '+971-2-505-2000',\n    email: '<EMAIL>',\n    website: 'https://www.adti.ac.ae',\n    graduation_date: '2024-06-25',\n    student_count: 2800,\n    is_active: false,\n    settings: {\n      graduation_ceremony_location: 'مركز التدريب',\n      dress_code: 'casual',\n      photography_allowed: true\n    },\n    created_at: '2024-01-20T15:45:00Z',\n    updated_at: '2024-02-10T12:00:00Z'\n  }\n]\n\n// بيانات وهمية للطلبات\nexport const mockOrders: MockOrder[] = [\n  {\n    id: '1',\n    order_number: 'GT-240120-001',\n    customer_id: 'student-1',\n    customer_name: 'أحمد محمد علي',\n    customer_email: '<EMAIL>',\n    customer_phone: '+971-50-123-4567',\n    status: 'in_production',\n    items: [\n      {\n        id: '1',\n        order_id: '1',\n        product_id: '1',\n        product_name: 'ثوب التخرج الكلاسيكي',\n        product_image: '/images/products/gown-classic-1.jpg',\n        category: 'gown',\n        quantity: 1,\n        unit_price: 299.99,\n        total_price: 299.99,\n        customizations: {\n          color: 'أسود',\n          size: 'L',\n          embroidery: 'أحمد علي - بكالوريوس هندسة'\n        }\n      },\n      {\n        id: '2',\n        order_id: '1',\n        product_id: '2',\n        product_name: 'قبعة التخرج الأكاديمية',\n        product_image: '/images/products/cap-academic-1.jpg',\n        category: 'cap',\n        quantity: 1,\n        unit_price: 89.99,\n        total_price: 89.99,\n        customizations: {\n          color: 'أسود',\n          size: 'M'\n        }\n      }\n    ],\n    subtotal: 389.98,\n    tax: 19.50,\n    shipping_cost: 25.00,\n    total: 434.48,\n    payment_status: 'paid',\n    payment_method: 'credit_card',\n    shipping_address: {\n      street: 'شارع الجامعة، مبنى 12، شقة 304',\n      city: 'العين',\n      state: 'أبوظبي',\n      postal_code: '17666',\n      country: 'الإمارات العربية المتحدة'\n    },\n    tracking_number: 'TRK-GT-001-2024',\n    notes: 'يرجى التسليم قبل حفل التخرج',\n    created_at: '2024-01-20T10:30:00Z',\n    updated_at: '2024-01-22T14:15:00Z',\n    delivery_date: '2024-02-15T00:00:00Z',\n    school_id: '1',\n    school_name: 'جامعة الإمارات العربية المتحدة'\n  },\n  {\n    id: '2',\n    order_number: 'GT-**********',\n    customer_id: 'student-2',\n    customer_name: 'فاطمة سالم الزهراني',\n    customer_email: '<EMAIL>',\n    customer_phone: '+971-56-789-0123',\n    status: 'delivered',\n    items: [\n      {\n        id: '3',\n        order_id: '2',\n        product_id: '3',\n        product_name: 'ثوب التخرج المميز',\n        product_image: '/images/products/gown-premium-1.jpg',\n        category: 'gown',\n        quantity: 1,\n        unit_price: 399.99,\n        total_price: 399.99,\n        customizations: {\n          color: 'أزرق داكن',\n          size: 'M',\n          embroidery: 'فاطمة الزهراني - ماجستير إدارة أعمال'\n        }\n      }\n    ],\n    subtotal: 399.99,\n    tax: 20.00,\n    shipping_cost: 30.00,\n    total: 449.99,\n    payment_status: 'paid',\n    payment_method: 'bank_transfer',\n    shipping_address: {\n      street: 'شارع الكورنيش، برج الإمارات، الطابق 15',\n      city: 'الشارقة',\n      state: 'الشارقة',\n      postal_code: '27272',\n      country: 'الإمارات العربية المتحدة'\n    },\n    tracking_number: 'TRK-GT-002-2024',\n    created_at: '2024-01-21T09:15:00Z',\n    updated_at: '2024-01-25T16:30:00Z',\n    delivery_date: '2024-01-28T00:00:00Z',\n    school_id: '2',\n    school_name: 'الجامعة الأمريكية في الشارقة'\n  },\n  {\n    id: '3',\n    order_number: 'GT-**********',\n    customer_id: 'student-3',\n    customer_name: 'خالد عبدالله المنصوري',\n    customer_email: '<EMAIL>',\n    customer_phone: '+971-52-456-7890',\n    status: 'pending',\n    items: [\n      {\n        id: '4',\n        order_id: '3',\n        product_id: '1',\n        product_name: 'ثوب التخرج الكلاسيكي',\n        product_image: '/images/products/gown-classic-1.jpg',\n        category: 'gown',\n        quantity: 1,\n        unit_price: 299.99,\n        total_price: 299.99,\n        customizations: {\n          color: 'بورجوندي',\n          size: 'XL'\n        }\n      },\n      {\n        id: '5',\n        order_id: '3',\n        product_id: '4',\n        product_name: 'وشاح التخرج المطرز',\n        product_image: '/images/products/stole-embroidered-1.jpg',\n        category: 'stole',\n        quantity: 1,\n        unit_price: 149.99,\n        total_price: 149.99,\n        customizations: {\n          color: 'ذهبي',\n          embroidery: 'كلية الهندسة'\n        }\n      }\n    ],\n    subtotal: 449.98,\n    tax: 22.50,\n    shipping_cost: 25.00,\n    total: 497.48,\n    payment_status: 'pending',\n    shipping_address: {\n      street: 'شارع الشيخ زايد، مجمع دبي الأكاديمي',\n      city: 'دبي',\n      state: 'دبي',\n      postal_code: '391186',\n      country: 'الإمارات العربية المتحدة'\n    },\n    created_at: '2024-01-22T14:45:00Z',\n    updated_at: '2024-01-22T14:45:00Z',\n    school_id: '3',\n    school_name: 'جامعة زايد'\n  }\n]\n\n// مساعدات للتعامل مع البيانات الوهمية\nexport class MockDataManager {\n  private static getStorageKey(type: 'pages' | 'menuItems' | 'products' | 'categories' | 'schools' | 'orders' | 'aiProviders'): string {\n    return `mockData_${type}`\n  }\n\n  static getPages(): MockPage[] {\n    if (typeof window === 'undefined') return mockPages\n\n    const stored = localStorage.getItem(this.getStorageKey('pages'))\n    return stored ? JSON.parse(stored) : mockPages\n  }\n\n  static getMenuItems(): MockMenuItem[] {\n    if (typeof window === 'undefined') return mockMenuItems\n\n    const stored = localStorage.getItem(this.getStorageKey('menuItems'))\n    return stored ? JSON.parse(stored) : mockMenuItems\n  }\n\n  static getProducts(): MockProduct[] {\n    if (typeof window === 'undefined') return mockProducts\n\n    const stored = localStorage.getItem(this.getStorageKey('products'))\n    return stored ? JSON.parse(stored) : mockProducts\n  }\n\n  static getCategories(): MockCategory[] {\n    if (typeof window === 'undefined') return mockCategories\n\n    const stored = localStorage.getItem(this.getStorageKey('categories'))\n    return stored ? JSON.parse(stored) : mockCategories\n  }\n\n  static getSchools(): MockSchool[] {\n    if (typeof window === 'undefined') return mockSchools\n\n    const stored = localStorage.getItem(this.getStorageKey('schools'))\n    return stored ? JSON.parse(stored) : mockSchools\n  }\n\n  static getOrders(): MockOrder[] {\n    if (typeof window === 'undefined') return mockOrders\n\n    const stored = localStorage.getItem(this.getStorageKey('orders'))\n    return stored ? JSON.parse(stored) : mockOrders\n  }\n\n  static savePages(pages: MockPage[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('pages'), JSON.stringify(pages))\n    }\n  }\n\n  static saveMenuItems(items: MockMenuItem[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('menuItems'), JSON.stringify(items))\n    }\n  }\n\n  static saveProducts(products: MockProduct[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('products'), JSON.stringify(products))\n    }\n  }\n\n  static saveCategories(categories: MockCategory[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('categories'), JSON.stringify(categories))\n    }\n  }\n\n  static saveSchools(schools: MockSchool[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('schools'), JSON.stringify(schools))\n    }\n  }\n\n  static saveOrders(orders: MockOrder[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('orders'), JSON.stringify(orders))\n    }\n  }\n\n  static generateId(): string {\n    return Date.now().toString() + Math.random().toString(36).substring(2, 11)\n  }\n\n  // مسح جميع البيانات المحفوظة (للاختبار)\n  static clearAllData(): void {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('mockAIModels')\n      localStorage.removeItem('mockModelActivities')\n      localStorage.removeItem('mockPages')\n      localStorage.removeItem('mockPageTemplates')\n      localStorage.removeItem('mockPageProjects')\n      localStorage.removeItem('mockComponentLibrary')\n    }\n  }\n\n  static generateOrderNumber(): string {\n    const date = new Date()\n    const year = date.getFullYear().toString().slice(-2)\n    const month = (date.getMonth() + 1).toString().padStart(2, '0')\n    const day = date.getDate().toString().padStart(2, '0')\n    const orders = this.getOrders()\n    const todayOrders = orders.filter(order =>\n      order.created_at.startsWith(`${date.getFullYear()}-${month}-${day}`)\n    )\n    const orderCount = (todayOrders.length + 1).toString().padStart(3, '0')\n    return `GT-${year}${month}${day}-${orderCount}`\n  }\n\n  // إدارة نماذج الذكاء الاصطناعي\n  static getAIModels(): AIModel[] {\n    if (typeof window === 'undefined') return this.defaultAIModels\n\n    const stored = localStorage.getItem('mockAIModels')\n    if (stored) {\n      return JSON.parse(stored)\n    }\n    return this.defaultAIModels\n  }\n\n  static saveAIModels(models: AIModel[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('mockAIModels', JSON.stringify(models))\n    }\n  }\n\n  static getModelActivities(): ModelActivity[] {\n    if (typeof window === 'undefined') return this.defaultModelActivities\n\n    const stored = localStorage.getItem('mockModelActivities')\n    if (stored) {\n      return JSON.parse(stored)\n    }\n    return this.defaultModelActivities\n  }\n\n  static saveModelActivities(activities: ModelActivity[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('mockModelActivities', JSON.stringify(activities))\n    }\n  }\n\n  // إدارة قوالب الصفحات\n  static getPageTemplates(): PageTemplate[] {\n    if (typeof window === 'undefined') return this.defaultPageTemplates\n\n    const stored = localStorage.getItem('mockPageTemplates')\n    if (stored) {\n      return JSON.parse(stored)\n    }\n    return this.defaultPageTemplates\n  }\n\n  static savePageTemplates(templates: PageTemplate[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('mockPageTemplates', JSON.stringify(templates))\n    }\n  }\n\n  static getPageProjects(): PageProject[] {\n    if (typeof window === 'undefined') return this.defaultPageProjects\n\n    const stored = localStorage.getItem('mockPageProjects')\n    if (stored) {\n      return JSON.parse(stored)\n    }\n    return this.defaultPageProjects\n  }\n\n  static savePageProjects(projects: PageProject[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('mockPageProjects', JSON.stringify(projects))\n    }\n  }\n\n  static getComponentLibrary(): ComponentLibraryItem[] {\n    if (typeof window === 'undefined') return this.defaultComponentLibrary\n\n    const stored = localStorage.getItem('mockComponentLibrary')\n    if (stored) {\n      return JSON.parse(stored)\n    }\n    return this.defaultComponentLibrary\n  }\n\n  static saveComponentLibrary(components: ComponentLibraryItem[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('mockComponentLibrary', JSON.stringify(components))\n    }\n  }\n\n  // البيانات الافتراضية لنماذج الذكاء الاصطناعي (فارغة للبداية)\n  static defaultAIModels: AIModel[] = []\n\n  static defaultModelActivities: ModelActivity[] = []\n\n  // البيانات الافتراضية لقوالب الصفحات\n  static defaultPageTemplates: PageTemplate[] = [\n    {\n      id: 'template-landing-1',\n      name: 'Landing Page - Modern',\n      nameAr: 'صفحة هبوط - عصرية',\n      nameEn: 'Landing Page - Modern',\n      nameFr: 'Page d\\'atterrissage - Moderne',\n      description: 'قالب صفحة هبوط عصرية مع تصميم جذاب',\n      category: 'landing',\n      components: [\n        {\n          id: 'hero-1',\n          type: 'hero',\n          name: 'Hero Section',\n          props: {\n            content: 'مرحباً بكم في منصة أزياء التخرج',\n            style: { backgroundColor: '#1F2937', color: '#FFFFFF' }\n          },\n          position: { x: 0, y: 0 },\n          size: { width: '100%', height: '500px' },\n          isVisible: true\n        }\n      ],\n      preview: '/images/templates/landing-modern.jpg',\n      thumbnail: '/images/templates/landing-modern-thumb.jpg',\n      isAIGenerated: false,\n      isPremium: false,\n      tags: ['landing', 'modern', 'business'],\n      createdAt: '2024-01-01T00:00:00Z',\n      updatedAt: '2024-01-01T00:00:00Z',\n      usageCount: 45,\n      rating: 4.8,\n      metadata: {\n        colors: ['#1F2937', '#FFFFFF', '#3B82F6'],\n        fonts: ['Inter', 'Cairo'],\n        layout: 'single-page',\n        responsive: true\n      }\n    }\n  ]\n\n  // البيانات الافتراضية لمشاريع الصفحات\n  static defaultPageProjects: PageProject[] = [\n    {\n      id: 'project-1',\n      name: 'موقع أزياء التخرج الرئيسي',\n      description: 'الموقع الرئيسي لعرض منتجات أزياء التخرج',\n      components: [],\n      templateId: 'template-landing-1',\n      generationMode: 'template',\n      settings: {\n        title: 'أزياء التخرج - منصة مغربية متخصصة',\n        description: 'أول منصة مغربية لتأجير وبيع أزياء التخرج',\n        keywords: ['أزياء التخرج', 'تأجير', 'المغرب'],\n        language: 'ar',\n        direction: 'rtl'\n      },\n      isPublished: false,\n      createdAt: '2024-01-15T00:00:00Z',\n      updatedAt: '2024-01-20T10:30:00Z',\n      createdBy: 'admin-1',\n      version: 1\n    }\n  ]\n\n  // البيانات الافتراضية لمكتبة المكونات\n  static defaultComponentLibrary: ComponentLibraryItem[] = [\n    {\n      id: 'comp-hero',\n      name: 'Hero Section',\n      nameAr: 'قسم البطل',\n      type: 'hero',\n      category: 'layout',\n      description: 'قسم رئيسي جذاب في أعلى الصفحة',\n      icon: 'Layout',\n      preview: '/images/components/hero-preview.jpg',\n      defaultProps: {\n        content: 'عنوان رئيسي جذاب',\n        style: { backgroundColor: '#1F2937', color: '#FFFFFF' }\n      },\n      defaultSize: { width: '100%', height: '500px' },\n      isCustom: false,\n      isPremium: false,\n      tags: ['layout', 'header', 'hero'],\n      usageCount: 156\n    },\n    {\n      id: 'comp-button',\n      name: 'Button',\n      nameAr: 'زر',\n      type: 'button',\n      category: 'interactive',\n      description: 'زر تفاعلي قابل للتخصيص',\n      icon: 'MousePointer',\n      preview: '/images/components/button-preview.jpg',\n      defaultProps: {\n        content: 'انقر هنا',\n        style: { backgroundColor: '#3B82F6', color: '#FFFFFF' }\n      },\n      defaultSize: { width: '120px', height: '40px' },\n      isCustom: false,\n      isPremium: false,\n      tags: ['interactive', 'button', 'cta'],\n      usageCount: 234\n    }\n  ]\n\n  // إدارة مزودي الذكاء الاصطناعي\n  static getAIProviders(): MockAIProvider[] {\n    if (typeof window === 'undefined') return []\n\n    const stored = localStorage.getItem(this.getStorageKey('aiProviders'))\n    return stored ? JSON.parse(stored) : []\n  }\n\n  static addAIProvider(provider: MockAIProvider): MockAIProvider {\n    if (typeof window === 'undefined') return provider\n\n    const providers = this.getAIProviders()\n    providers.push(provider)\n    localStorage.setItem(this.getStorageKey('aiProviders'), JSON.stringify(providers))\n    return provider\n  }\n\n  static updateAIProvider(id: string, updatedProvider: Partial<MockAIProvider>): MockAIProvider | null {\n    if (typeof window === 'undefined') return null\n\n    const providers = this.getAIProviders()\n    const index = providers.findIndex(p => p.id === id)\n\n    if (index === -1) return null\n\n    providers[index] = { ...providers[index], ...updatedProvider }\n    localStorage.setItem(this.getStorageKey('aiProviders'), JSON.stringify(providers))\n    return providers[index]\n  }\n\n  static deleteAIProvider(id: string): boolean {\n    if (typeof window === 'undefined') return false\n\n    const providers = this.getAIProviders()\n    const filteredProviders = providers.filter(p => p.id !== id)\n\n    if (filteredProviders.length === providers.length) return false\n\n    localStorage.setItem(this.getStorageKey('aiProviders'), JSON.stringify(filteredProviders))\n    return true\n  }\n\n  static getAIProviderById(id: string): MockAIProvider | null {\n    if (typeof window === 'undefined') return null\n\n    const providers = this.getAIProviders()\n    return providers.find(p => p.id === id) || null\n  }\n}\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;;;;;AAiK1B,MAAM,YAAwB;IACnC;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,WAAW;QACX,gBAAgB;QAChB,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,WAAW;QACb;QACA,cAAc;YACZ;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,WAAW;QACb;QACA,cAAc;YACZ;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,WAAW;QACb;QACA,cAAc;YACZ;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;SACD;IACH;CACD;AAGM,MAAM,gBAAgC;IAC3C;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,WAAW;QACX,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,WAAW;QACX,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,iBAAiC;IAC5C;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,eAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAQ;YAAa;SAAW;QACzC,OAAO;YAAC;YAAK;YAAK;YAAK;YAAM;SAAM;QACnC,QAAQ;YAAC;YAAuC;SAAsC;QACtF,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAkB;YAAe;SAAoB;QAChE,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,MAAM;QACR;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAQ;SAAY;QAC7B,OAAO;YAAC;SAAW;QACnB,QAAQ;YAAC;SAAyC;QAClD,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAA0B;YAAe;SAAe;QACnE,gBAAgB;YACd,UAAU;YACV,cAAc;YACd,YAAY;QACd;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAgB;YAAe;SAAe;QACvD,OAAO;YAAC;SAAW;QACnB,QAAQ;YAAC;YAA4C;SAA2C;QAChG,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAc;YAAc;SAAa;QACpD,gBAAgB;YACd,UAAU;YACV,YAAY;YACZ,QAAQ;QACV;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAQ;YAAO;YAAQ;SAAO;QACvC,OAAO;YAAC;SAAW;QACnB,QAAQ;YAAC;SAAqC;QAC9C,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAqB;YAAe;SAAc;QAC7D,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,YAAY;QACd;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAgB;SAAc;QACvC,OAAO;YAAC;YAAK;YAAK;SAAK;QACvB,QAAQ;YAAC;YAAyC;SAAwC;QAC1F,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAsB;YAAc;SAAkB;QACjE,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,gBAAgB;QAClB;IACF;CACD;AAGM,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,aAA0B;IACrC;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,QAAQ;QACR,OAAO;YACL;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,MAAM;oBACN,YAAY;gBACd;YACF;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,MAAM;gBACR;YACF;SACD;QACD,UAAU;QACV,KAAK;QACL,eAAe;QACf,OAAO;QACP,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;YAChB,QAAQ;YACR,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA,iBAAiB;QACjB,OAAO;QACP,YAAY;QACZ,YAAY;QACZ,eAAe;QACf,WAAW;QACX,aAAa;IACf;IACA;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,QAAQ;QACR,OAAO;YACL;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,MAAM;oBACN,YAAY;gBACd;YACF;SACD;QACD,UAAU;QACV,KAAK;QACL,eAAe;QACf,OAAO;QACP,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;YAChB,QAAQ;YACR,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA,iBAAiB;QACjB,YAAY;QACZ,YAAY;QACZ,eAAe;QACf,WAAW;QACX,aAAa;IACf;IACA;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,QAAQ;QACR,OAAO;YACL;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,MAAM;gBACR;YACF;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,YAAY;gBACd;YACF;SACD;QACD,UAAU;QACV,KAAK;QACL,eAAe;QACf,OAAO;QACP,gBAAgB;QAChB,kBAAkB;YAChB,QAAQ;YACR,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA,YAAY;QACZ,YAAY;QACZ,WAAW;QACX,aAAa;IACf;CACD;AAGM,MAAM;IACX,OAAe,cAAc,IAA8F,EAAU;QACnI,OAAO,CAAC,SAAS,EAAE,MAAM;IAC3B;IAEA,OAAO,WAAuB;QAC5B,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,eAA+B;QACpC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,cAA6B;QAClC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,gBAAgC;QACrC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,aAA2B;QAChC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,YAAyB;QAC9B,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,UAAU,KAAiB,EAAQ;QACxC,uCAAmC;;QAEnC;IACF;IAEA,OAAO,cAAc,KAAqB,EAAQ;QAChD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,aAAa,QAAuB,EAAQ;QACjD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,eAAe,UAA0B,EAAQ;QACtD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,YAAY,OAAqB,EAAQ;QAC9C,uCAAmC;;QAEnC;IACF;IAEA,OAAO,WAAW,MAAmB,EAAQ;QAC3C,uCAAmC;;QAEnC;IACF;IAEA,OAAO,aAAqB;QAC1B,OAAO,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACzE;IAEA,wCAAwC;IACxC,OAAO,eAAqB;QAC1B,uCAAmC;;QAOnC;IACF;IAEA,OAAO,sBAA8B;QACnC,MAAM,OAAO,IAAI;QACjB,MAAM,OAAO,KAAK,WAAW,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;QAClD,MAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;QAC3D,MAAM,MAAM,KAAK,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;QAClD,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,MAAM,cAAc,OAAO,MAAM,CAAC,CAAA,QAChC,MAAM,UAAU,CAAC,UAAU,CAAC,GAAG,KAAK,WAAW,GAAG,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK;QAErE,MAAM,aAAa,CAAC,YAAY,MAAM,GAAG,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;QACnE,OAAO,CAAC,GAAG,EAAE,OAAO,QAAQ,IAAI,CAAC,EAAE,YAAY;IACjD;IAEA,+BAA+B;IAC/B,OAAO,cAAyB;QAC9B,wCAAmC,OAAO,IAAI,CAAC,eAAe;;QAE9D,MAAM;IAKR;IAEA,OAAO,aAAa,MAAiB,EAAQ;QAC3C,uCAAmC;;QAEnC;IACF;IAEA,OAAO,qBAAsC;QAC3C,wCAAmC,OAAO,IAAI,CAAC,sBAAsB;;QAErE,MAAM;IAKR;IAEA,OAAO,oBAAoB,UAA2B,EAAQ;QAC5D,uCAAmC;;QAEnC;IACF;IAEA,sBAAsB;IACtB,OAAO,mBAAmC;QACxC,wCAAmC,OAAO,IAAI,CAAC,oBAAoB;;QAEnE,MAAM;IAKR;IAEA,OAAO,kBAAkB,SAAyB,EAAQ;QACxD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,kBAAiC;QACtC,wCAAmC,OAAO,IAAI,CAAC,mBAAmB;;QAElE,MAAM;IAKR;IAEA,OAAO,iBAAiB,QAAuB,EAAQ;QACrD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,sBAA8C;QACnD,wCAAmC,OAAO,IAAI,CAAC,uBAAuB;;QAEtE,MAAM;IAKR;IAEA,OAAO,qBAAqB,UAAkC,EAAQ;QACpE,uCAAmC;;QAEnC;IACF;IAEA,8DAA8D;IAC9D,OAAO,kBAA6B,EAAE,CAAA;IAEtC,OAAO,yBAA0C,EAAE,CAAA;IAEnD,qCAAqC;IACrC,OAAO,uBAAuC;QAC5C;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,aAAa;YACb,UAAU;YACV,YAAY;gBACV;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,OAAO;wBACL,SAAS;wBACT,OAAO;4BAAE,iBAAiB;4BAAW,OAAO;wBAAU;oBACxD;oBACA,UAAU;wBAAE,GAAG;wBAAG,GAAG;oBAAE;oBACvB,MAAM;wBAAE,OAAO;wBAAQ,QAAQ;oBAAQ;oBACvC,WAAW;gBACb;aACD;YACD,SAAS;YACT,WAAW;YACX,eAAe;YACf,WAAW;YACX,MAAM;gBAAC;gBAAW;gBAAU;aAAW;YACvC,WAAW;YACX,WAAW;YACX,YAAY;YACZ,QAAQ;YACR,UAAU;gBACR,QAAQ;oBAAC;oBAAW;oBAAW;iBAAU;gBACzC,OAAO;oBAAC;oBAAS;iBAAQ;gBACzB,QAAQ;gBACR,YAAY;YACd;QACF;KACD,CAAA;IAED,sCAAsC;IACtC,OAAO,sBAAqC;QAC1C;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,YAAY,EAAE;YACd,YAAY;YACZ,gBAAgB;YAChB,UAAU;gBACR,OAAO;gBACP,aAAa;gBACb,UAAU;oBAAC;oBAAgB;oBAAS;iBAAS;gBAC7C,UAAU;gBACV,WAAW;YACb;YACA,aAAa;YACb,WAAW;YACX,WAAW;YACX,WAAW;YACX,SAAS;QACX;KACD,CAAA;IAED,sCAAsC;IACtC,OAAO,0BAAkD;QACvD;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,MAAM;YACN,UAAU;YACV,aAAa;YACb,MAAM;YACN,SAAS;YACT,cAAc;gBACZ,SAAS;gBACT,OAAO;oBAAE,iBAAiB;oBAAW,OAAO;gBAAU;YACxD;YACA,aAAa;gBAAE,OAAO;gBAAQ,QAAQ;YAAQ;YAC9C,UAAU;YACV,WAAW;YACX,MAAM;gBAAC;gBAAU;gBAAU;aAAO;YAClC,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,MAAM;YACN,UAAU;YACV,aAAa;YACb,MAAM;YACN,SAAS;YACT,cAAc;gBACZ,SAAS;gBACT,OAAO;oBAAE,iBAAiB;oBAAW,OAAO;gBAAU;YACxD;YACA,aAAa;gBAAE,OAAO;gBAAS,QAAQ;YAAO;YAC9C,UAAU;YACV,WAAW;YACX,MAAM;gBAAC;gBAAe;gBAAU;aAAM;YACtC,YAAY;QACd;KACD,CAAA;IAED,+BAA+B;IAC/B,OAAO,iBAAmC;QACxC,wCAAmC,OAAO,EAAE;;QAE5C,MAAM;IAER;IAEA,OAAO,cAAc,QAAwB,EAAkB;QAC7D,wCAAmC,OAAO;;QAE1C,MAAM;IAIR;IAEA,OAAO,iBAAiB,EAAU,EAAE,eAAwC,EAAyB;QACnG,wCAAmC,OAAO;;QAE1C,MAAM;QACN,MAAM;IAOR;IAEA,OAAO,iBAAiB,EAAU,EAAW;QAC3C,wCAAmC,OAAO;;QAE1C,MAAM;QACN,MAAM;IAMR;IAEA,OAAO,kBAAkB,EAAU,EAAyB;QAC1D,wCAAmC,OAAO;;QAE1C,MAAM;IAER;AACF", "debugId": null}}, {"offset": {"line": 1121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/api/page-builder/templates/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { MockDataManager } from '@/lib/mockData'\nimport { TemplateCategory } from '@/types/page-builder'\n\n// GET - جلب جميع قوالب الصفحات\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const category = searchParams.get('category') as TemplateCategory | null\n    const language = searchParams.get('language') || 'ar'\n    const includePremium = searchParams.get('include_premium') === 'true'\n    const sortBy = searchParams.get('sort_by') || 'usageCount'\n    const sortOrder = searchParams.get('sort_order') || 'desc'\n    const page = parseInt(searchParams.get('page') || '1')\n    const limit = parseInt(searchParams.get('limit') || '20')\n\n    // جلب البيانات الوهمية\n    let templates = MockDataManager.getPageTemplates()\n\n    // تطبيق الفلاتر\n    if (category) {\n      templates = templates.filter(template => template.category === category)\n    }\n\n    if (!includePremium) {\n      templates = templates.filter(template => !template.isPremium)\n    }\n\n    // الترتيب\n    templates.sort((a, b) => {\n      const aValue = a[sortBy as keyof typeof a] as number\n      const bValue = b[sortBy as keyof typeof b] as number\n      \n      if (sortOrder === 'desc') {\n        return bValue - aValue\n      } else {\n        return aValue - bValue\n      }\n    })\n\n    // تطبيق التصفح\n    const startIndex = (page - 1) * limit\n    const endIndex = startIndex + limit\n    const paginatedTemplates = templates.slice(startIndex, endIndex)\n\n    // إحصائيات الفئات\n    const categories = Array.from(new Set(MockDataManager.getPageTemplates().map(t => t.category)))\n    const categoryStats = categories.map(cat => ({\n      category: cat,\n      count: MockDataManager.getPageTemplates().filter(t => t.category === cat).length,\n      premiumCount: MockDataManager.getPageTemplates().filter(t => t.category === cat && t.isPremium).length\n    }))\n\n    return NextResponse.json({\n      templates: paginatedTemplates,\n      total: templates.length,\n      page,\n      limit,\n      totalPages: Math.ceil(templates.length / limit),\n      categories: categoryStats,\n      stats: {\n        total: MockDataManager.getPageTemplates().length,\n        free: MockDataManager.getPageTemplates().filter(t => !t.isPremium).length,\n        premium: MockDataManager.getPageTemplates().filter(t => t.isPremium).length,\n        aiGenerated: MockDataManager.getPageTemplates().filter(t => t.isAIGenerated).length\n      }\n    })\n\n  } catch (error) {\n    console.error('Error fetching page templates:', error)\n    return NextResponse.json(\n      { error: 'خطأ في جلب قوالب الصفحات' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST - إنشاء قالب جديد\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const {\n      name,\n      nameAr,\n      nameEn,\n      nameFr,\n      description,\n      category,\n      components,\n      preview,\n      thumbnail,\n      isAIGenerated,\n      isPremium,\n      tags,\n      metadata\n    } = body\n\n    // التحقق من البيانات المطلوبة\n    if (!name || !nameAr || !category || !components) {\n      return NextResponse.json(\n        { error: 'الاسم والفئة والمكونات مطلوبة' },\n        { status: 400 }\n      )\n    }\n\n    // جلب القوالب الحالية\n    const templates = MockDataManager.getPageTemplates()\n\n    // التحقق من عدم تكرار الاسم\n    const existingTemplate = templates.find(template => \n      template.name.toLowerCase() === name.toLowerCase() ||\n      template.nameAr.toLowerCase() === nameAr.toLowerCase()\n    )\n    if (existingTemplate) {\n      return NextResponse.json(\n        { error: 'قالب بنفس الاسم موجود بالفعل' },\n        { status: 400 }\n      )\n    }\n\n    // إنشاء القالب الجديد\n    const newTemplate = {\n      id: MockDataManager.generateId(),\n      name,\n      nameAr,\n      nameEn,\n      nameFr,\n      description,\n      category,\n      components: components.map((comp: any) => ({\n        ...comp,\n        id: comp.id || MockDataManager.generateId()\n      })),\n      preview: preview || '/images/templates/default-preview.jpg',\n      thumbnail: thumbnail || '/images/templates/default-thumb.jpg',\n      isAIGenerated: isAIGenerated || false,\n      isPremium: isPremium || false,\n      tags: tags || [],\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      usageCount: 0,\n      rating: 0,\n      metadata: metadata || {\n        colors: [],\n        fonts: [],\n        layout: 'single-page',\n        responsive: true\n      }\n    }\n\n    // حفظ القالب\n    templates.push(newTemplate)\n    MockDataManager.savePageTemplates(templates)\n\n    return NextResponse.json({ \n      message: 'تم إنشاء القالب بنجاح',\n      template: newTemplate \n    }, { status: 201 })\n\n  } catch (error) {\n    console.error('Error creating page template:', error)\n    return NextResponse.json(\n      { error: 'خطأ في إنشاء القالب' },\n      { status: 500 }\n    )\n  }\n}\n\n// PUT - تحديث إحصائيات الاستخدام\nexport async function PUT(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { templateId, action } = body\n\n    if (!templateId || !action) {\n      return NextResponse.json(\n        { error: 'معرف القالب والإجراء مطلوبان' },\n        { status: 400 }\n      )\n    }\n\n    const templates = MockDataManager.getPageTemplates()\n    const templateIndex = templates.findIndex(t => t.id === templateId)\n\n    if (templateIndex === -1) {\n      return NextResponse.json(\n        { error: 'القالب غير موجود' },\n        { status: 404 }\n      )\n    }\n\n    const template = templates[templateIndex]\n\n    switch (action) {\n      case 'increment_usage':\n        template.usageCount += 1\n        break\n      case 'rate':\n        const { rating } = body\n        if (rating >= 1 && rating <= 5) {\n          // حساب متوسط التقييم (مبسط)\n          template.rating = rating\n        }\n        break\n      default:\n        return NextResponse.json(\n          { error: 'إجراء غير مدعوم' },\n          { status: 400 }\n        )\n    }\n\n    template.updatedAt = new Date().toISOString()\n    templates[templateIndex] = template\n    MockDataManager.savePageTemplates(templates)\n\n    return NextResponse.json({\n      message: 'تم تحديث القالب بنجاح',\n      template\n    })\n\n  } catch (error) {\n    console.error('Error updating template:', error)\n    return NextResponse.json(\n      { error: 'خطأ في تحديث القالب' },\n      { status: 500 }\n    )\n  }\n}\n\n// DELETE - حذف قالب\nexport async function DELETE(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const templateId = searchParams.get('id')\n\n    if (!templateId) {\n      return NextResponse.json(\n        { error: 'معرف القالب مطلوب' },\n        { status: 400 }\n      )\n    }\n\n    const templates = MockDataManager.getPageTemplates()\n    const templateIndex = templates.findIndex(t => t.id === templateId)\n\n    if (templateIndex === -1) {\n      return NextResponse.json(\n        { error: 'القالب غير موجود' },\n        { status: 404 }\n      )\n    }\n\n    const template = templates[templateIndex]\n\n    // التحقق من استخدام القالب في مشاريع موجودة\n    const projects = MockDataManager.getPageProjects()\n    const usedInProjects = projects.filter(p => p.templateId === templateId)\n\n    if (usedInProjects.length > 0) {\n      return NextResponse.json(\n        { \n          error: 'لا يمكن حذف القالب لأنه مستخدم في مشاريع موجودة',\n          usedInProjects: usedInProjects.map(p => ({ id: p.id, name: p.name }))\n        },\n        { status: 400 }\n      )\n    }\n\n    // حذف القالب\n    templates.splice(templateIndex, 1)\n    MockDataManager.savePageTemplates(templates)\n\n    return NextResponse.json({\n      message: 'تم حذف القالب بنجاح',\n      deletedTemplate: {\n        id: template.id,\n        name: template.name,\n        nameAr: template.nameAr\n      }\n    })\n\n  } catch (error) {\n    console.error('Error deleting template:', error)\n    return NextResponse.json(\n      { error: 'خطأ في حذف القالب' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAIO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,WAAW,aAAa,GAAG,CAAC,eAAe;QACjD,MAAM,iBAAiB,aAAa,GAAG,CAAC,uBAAuB;QAC/D,MAAM,SAAS,aAAa,GAAG,CAAC,cAAc;QAC9C,MAAM,YAAY,aAAa,GAAG,CAAC,iBAAiB;QACpD,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QAEpD,uBAAuB;QACvB,IAAI,YAAY,wHAAA,CAAA,kBAAe,CAAC,gBAAgB;QAEhD,gBAAgB;QAChB,IAAI,UAAU;YACZ,YAAY,UAAU,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ,KAAK;QACjE;QAEA,IAAI,CAAC,gBAAgB;YACnB,YAAY,UAAU,MAAM,CAAC,CAAA,WAAY,CAAC,SAAS,SAAS;QAC9D;QAEA,UAAU;QACV,UAAU,IAAI,CAAC,CAAC,GAAG;YACjB,MAAM,SAAS,CAAC,CAAC,OAAyB;YAC1C,MAAM,SAAS,CAAC,CAAC,OAAyB;YAE1C,IAAI,cAAc,QAAQ;gBACxB,OAAO,SAAS;YAClB,OAAO;gBACL,OAAO,SAAS;YAClB;QACF;QAEA,eAAe;QACf,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;QAChC,MAAM,WAAW,aAAa;QAC9B,MAAM,qBAAqB,UAAU,KAAK,CAAC,YAAY;QAEvD,kBAAkB;QAClB,MAAM,aAAa,MAAM,IAAI,CAAC,IAAI,IAAI,wHAAA,CAAA,kBAAe,CAAC,gBAAgB,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;QAC5F,MAAM,gBAAgB,WAAW,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC3C,UAAU;gBACV,OAAO,wHAAA,CAAA,kBAAe,CAAC,gBAAgB,GAAG,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,KAAK,MAAM;gBAChF,cAAc,wHAAA,CAAA,kBAAe,CAAC,gBAAgB,GAAG,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,OAAO,EAAE,SAAS,EAAE,MAAM;YACxG,CAAC;QAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,WAAW;YACX,OAAO,UAAU,MAAM;YACvB;YACA;YACA,YAAY,KAAK,IAAI,CAAC,UAAU,MAAM,GAAG;YACzC,YAAY;YACZ,OAAO;gBACL,OAAO,wHAAA,CAAA,kBAAe,CAAC,gBAAgB,GAAG,MAAM;gBAChD,MAAM,wHAAA,CAAA,kBAAe,CAAC,gBAAgB,GAAG,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,SAAS,EAAE,MAAM;gBACzE,SAAS,wHAAA,CAAA,kBAAe,CAAC,gBAAgB,GAAG,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;gBAC3E,aAAa,wHAAA,CAAA,kBAAe,CAAC,gBAAgB,GAAG,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,EAAE,MAAM;YACrF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,IAAI,EACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,WAAW,EACX,QAAQ,EACR,UAAU,EACV,OAAO,EACP,SAAS,EACT,aAAa,EACb,SAAS,EACT,IAAI,EACJ,QAAQ,EACT,GAAG;QAEJ,8BAA8B;QAC9B,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,YAAY;YAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgC,GACzC;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,YAAY,wHAAA,CAAA,kBAAe,CAAC,gBAAgB;QAElD,4BAA4B;QAC5B,MAAM,mBAAmB,UAAU,IAAI,CAAC,CAAA,WACtC,SAAS,IAAI,CAAC,WAAW,OAAO,KAAK,WAAW,MAChD,SAAS,MAAM,CAAC,WAAW,OAAO,OAAO,WAAW;QAEtD,IAAI,kBAAkB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+B,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,cAAc;YAClB,IAAI,wHAAA,CAAA,kBAAe,CAAC,UAAU;YAC9B;YACA;YACA;YACA;YACA;YACA;YACA,YAAY,WAAW,GAAG,CAAC,CAAC,OAAc,CAAC;oBACzC,GAAG,IAAI;oBACP,IAAI,KAAK,EAAE,IAAI,wHAAA,CAAA,kBAAe,CAAC,UAAU;gBAC3C,CAAC;YACD,SAAS,WAAW;YACpB,WAAW,aAAa;YACxB,eAAe,iBAAiB;YAChC,WAAW,aAAa;YACxB,MAAM,QAAQ,EAAE;YAChB,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;YACjC,YAAY;YACZ,QAAQ;YACR,UAAU,YAAY;gBACpB,QAAQ,EAAE;gBACV,OAAO,EAAE;gBACT,QAAQ;gBACR,YAAY;YACd;QACF;QAEA,aAAa;QACb,UAAU,IAAI,CAAC;QACf,wHAAA,CAAA,kBAAe,CAAC,iBAAiB,CAAC;QAElC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,UAAU;QACZ,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAsB,GAC/B;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG;QAE/B,IAAI,CAAC,cAAc,CAAC,QAAQ;YAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+B,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,YAAY,wHAAA,CAAA,kBAAe,CAAC,gBAAgB;QAClD,MAAM,gBAAgB,UAAU,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAExD,IAAI,kBAAkB,CAAC,GAAG;YACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW,SAAS,CAAC,cAAc;QAEzC,OAAQ;YACN,KAAK;gBACH,SAAS,UAAU,IAAI;gBACvB;YACF,KAAK;gBACH,MAAM,EAAE,MAAM,EAAE,GAAG;gBACnB,IAAI,UAAU,KAAK,UAAU,GAAG;oBAC9B,4BAA4B;oBAC5B,SAAS,MAAM,GAAG;gBACpB;gBACA;YACF;gBACE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAkB,GAC3B;oBAAE,QAAQ;gBAAI;QAEpB;QAEA,SAAS,SAAS,GAAG,IAAI,OAAO,WAAW;QAC3C,SAAS,CAAC,cAAc,GAAG;QAC3B,wHAAA,CAAA,kBAAe,CAAC,iBAAiB,CAAC;QAElC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAsB,GAC/B;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OAAO,OAAoB;IAC/C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,aAAa,aAAa,GAAG,CAAC;QAEpC,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,YAAY,wHAAA,CAAA,kBAAe,CAAC,gBAAgB;QAClD,MAAM,gBAAgB,UAAU,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAExD,IAAI,kBAAkB,CAAC,GAAG;YACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW,SAAS,CAAC,cAAc;QAEzC,4CAA4C;QAC5C,MAAM,WAAW,wHAAA,CAAA,kBAAe,CAAC,eAAe;QAChD,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK;QAE7D,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,gBAAgB,eAAe,GAAG,CAAC,CAAA,IAAK,CAAC;wBAAE,IAAI,EAAE,EAAE;wBAAE,MAAM,EAAE,IAAI;oBAAC,CAAC;YACrE,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,aAAa;QACb,UAAU,MAAM,CAAC,eAAe;QAChC,wHAAA,CAAA,kBAAe,CAAC,iBAAiB,CAAC;QAElC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,iBAAiB;gBACf,IAAI,SAAS,EAAE;gBACf,MAAM,SAAS,IAAI;gBACnB,QAAQ,SAAS,MAAM;YACzB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAoB,GAC7B;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}